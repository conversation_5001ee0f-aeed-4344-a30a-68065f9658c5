﻿#include "pch.h"
#include "check.h"

HHBUI::UICheck::UICheck(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT nID, INT dwStyle, INT dwStyleEx, INT textFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-check", lpszName, dwStyle, dwStyleEx, nID, textFormat);
	p_data.boxs = width < height ? width - 2 : height - 2;
	SetBoxSize(16);
	p_data.hBrush = new UIBrush();
}

void HHBUI::UICheck::SetRadio(BOOL isRadio)
{
	p_data.radio = isRadio;
}

void HHBUI::UICheck::SetBoxPosition(BOOL isLeft)
{
	p_data.isl = isLeft;
}

void HHBUI::UICheck::SetBoxColor(UIColor nor, UIColor checked, UIColor state)
{
	if (!nor.empty()) p_data.clr[0] = nor;
	if (!checked.empty()) p_data.clr[1] = checked;
	if (!state.empty()) p_data.clr[2] = state;
}

void HHBUI::UICheck::SetRadius(FLOAT radius)
{
	p_data.rad = radius;
}

void HHBUI::UICheck::SetBoxSize(UINT size)
{
	p_data.boxs = UIEngine::ScaleValue(size);
	calcPoints();
}

void HHBUI::UICheck::SetState(check_state state)
{
	if (p_data.radio)
	{
		if (state == unchecked)
		{
			if (!((m_data.dwState & state_checked) == state_checked))
			{
				return;
			}
		}
		else
		{
			OnBrothers(BM_SETCHECK, 0, 0, TRUE, TRUE);
		}

	}
	else
	{
		UIControl::SetState(state_halfselect | state_select, TRUE, m_data.Frame);
	}
	UIControl::SetState((state == indeterminate ? state_halfselect | state_checked : state_checked), state == unchecked, m_data.Frame);
}

HHBUI::check_state HHBUI::UICheck::GetState()
{
	if ((m_data.dwState & state_halfselect) == state_halfselect)
	{
		return indeterminate;
	}
	else if ((m_data.dwState & state_checked) == state_checked)
	{
		return checked;
	}
	return unchecked;
}

void HHBUI::UICheck::EnableIndeterminate(BOOL enable)
{
	p_data.eni = enable;
}


LRESULT HHBUI::UICheck::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_EX_LCLICK || uMsg == BM_CLICK)
	{
		if (uMsg == BM_CLICK)
		{
			DispatchNotify(WMM_CLICK, wParam, lParam);
		}
		INT dwState = unchecked;
		if (!p_data.radio)
		{
			dwState = (m_data.dwState & state_checked) != state_checked;
			SetState((check_state)dwState);
		}
		else
		{
			if ((m_data.dwState & state_checked) != state_checked)
			{
				dwState = checked;
				SetState((check_state)dwState);
			}
		}
		DispatchNotify(WMM_CHECK, dwState, lParam);
	}
	else if (uMsg == WM_LBUTTONDOWN) {
		UIControl::SetState(state_down, FALSE);
	}
	else if (uMsg == WM_LBUTTONUP)
	{
		UIControl::SetState(state_down, TRUE);
	}
	else if (uMsg == WM_SETFOCUS)
	{
		UIControl::SetState(state_hover, FALSE);
		Redraw();
	}
	else if (uMsg == WM_KILLFOCUS)
	{
		UIControl::SetState(state_hover, TRUE);
		Redraw();
	}
	else if (uMsg == WM_MOUSEHOVER)
	{
		UIControl::SetState(state_hover, FALSE);
		Redraw();
	}
	else if (uMsg == WM_MOUSELEAVE)
	{
		UIControl::SetState(state_hover | state_down, TRUE);
		Redraw();
	}
	else if (uMsg == BM_SETCHECK)
	{
		if (p_data.radio)
			UIControl::SetState(state_checked, wParam == unchecked);
		Redraw();
	}
	else if (uMsg == BM_GETCHECK)
	{
		return GetState();
	}
	else if (uMsg == WM_DESTROY)
	{
		delete p_data.hBrush;
	}
	return S_OK;
}

void HHBUI::UICheck::calcPoints()
{
	p_data.pots[0].x = p_data.boxs * 0.2f;
	p_data.pots[1].x = p_data.boxs * 0.8f;
	p_data.pots[0].y = p_data.pots[1].y = p_data.boxs / 2.f;

	p_data.pots[2].x = p_data.boxs * 0.2f;
	p_data.pots[2].y = p_data.boxs * 0.5f;
	p_data.pots[3].x = p_data.boxs * 0.4f;
	p_data.pots[3].y = p_data.boxs * 0.8f;
	p_data.pots[4].x = p_data.boxs * 0.8f;
	p_data.pots[4].y = p_data.boxs * 0.3f;
}

void HHBUI::UICheck::OnPaintProc(ps_context ps)
{
	p_data.hBrush->SetColor(p_data.clr[0]);
	UIColor color_text;
	GetColor(color_text_normal, color_text);
	if ((ps.dwState & state_checked) != 0)
	{
		p_data.hBrush->SetColor(p_data.clr[1]);
	}
	else if ((ps.dwState & state_disable) != 0)
	{
		GetColor(color_text_ban, color_text);
		if (color_text.empty())
			GetColor(color_text_normal, color_text);
	}
	else if ((ps.dwState & state_focus) != 0)
	{
		GetColor(color_focus, color_text);
		if (color_text.empty())
			GetColor(color_text_normal, color_text);
		p_data.hBrush->SetColor(p_data.clr[1]);
	}
	if ((ps.dwState & state_hover) != 0)
	{
		GetColor(color_text_hover, color_text);
		if (color_text.empty())
			GetColor(color_text_normal, color_text);
		color_text.SetColorLights(0.7f);
		p_data.hBrush->SetColor(p_data.clr[1]);
	}
	INT at = (ps.uHeight - p_data.boxs) / 2;
	LPCWSTR label = GetText();
	if ((ps.dwState & state_down) != 0 && (ps.dwStyle & eos_textoffset) != 0)
		ps.rcText.Offset(1 * ps.dpi, 1 * ps.dpi);
	if (p_data.isl) {
		if (p_data.radio)
		{
			if ((ps.dwState & state_checked) != state_checked) ps.hCanvas->DrawRoundRect(p_data.hBrush, 1.f, at, p_data.boxs, at + p_data.boxs, p_data.boxs / 2 * ps.dpi, 1.f);
			else ps.hCanvas->FillRoundRect(p_data.hBrush, 1.f, at, p_data.boxs, at + p_data.boxs, p_data.boxs / 2 * ps.dpi);
		}
		else
		{
			if ((ps.dwState & state_checked) != state_checked) ps.hCanvas->DrawRoundRect(p_data.hBrush, 1.f, at, p_data.boxs, at + p_data.boxs, p_data.rad * ps.dpi, 1.f);
			else ps.hCanvas->FillRoundRect(p_data.hBrush, 1.f, at, p_data.boxs, at + p_data.boxs, p_data.rad * ps.dpi);
		}
		p_data.hBrush->SetColor(p_data.clr[2]);
		if ((ps.dwState & state_halfselect) != 0) {
			ps.hCanvas->DrawLine(p_data.hBrush, 1.f + p_data.pots[0].x, at + p_data.pots[0].y, p_data.pots[1].x, at + p_data.pots[1].y, 3.f);
		}
		else if ((ps.dwState & state_checked) != 0)
		{
			ps.hCanvas->DrawLine(p_data.hBrush, 1.f + p_data.pots[2].x, at + p_data.pots[2].y, 1.f + p_data.pots[3].x, at + p_data.pots[3].y, 3.f, 0, TRUE);
			ps.hCanvas->DrawLine(p_data.hBrush, 1.f + p_data.pots[4].x, at + p_data.pots[4].y, 1.f + p_data.pots[3].x, at + p_data.pots[3].y, 3.f, 0, TRUE);
		}
		ps.hCanvas->DrawTextByColor(ps.hFont, label, ps.dwTextFormat, 3.f + p_data.boxs + ps.rcText.left, m_data.Frame_t.top, ps.rcText.right - 3.f, ps.rcText.bottom, color_text);
	}
	else {
		if (p_data.radio)
		{
			if ((ps.dwState & state_checked) != state_checked) ps.hCanvas->DrawRoundRect(p_data.hBrush, ps.uWidth - p_data.boxs - 1.f, at, ps.uWidth - 1.f, at + p_data.boxs, (p_data.boxs / 2) * ps.dpi, 1.f);
			else ps.hCanvas->FillRoundRect(p_data.hBrush, ps.uWidth - p_data.boxs - 1.f, at, ps.uWidth - 1.f, at + p_data.boxs, (p_data.boxs / 2) * ps.dpi);
		}
		else
		{
			if ((ps.dwState & state_checked) != state_checked) ps.hCanvas->DrawRoundRect(p_data.hBrush, ps.uWidth - p_data.boxs - 1.f, at, ps.uWidth - 1.f, at + p_data.boxs, p_data.rad * ps.dpi, 1.f);
			else ps.hCanvas->FillRoundRect(p_data.hBrush, ps.uWidth - p_data.boxs - 1.f, at, ps.uWidth - 1.f, at + p_data.boxs, p_data.rad * ps.dpi);
		}
		p_data.hBrush->SetColor(p_data.clr[2]);
		if ((ps.dwState & state_halfselect) != 0) {
			ps.hCanvas->DrawLine(p_data.hBrush, ps.uWidth - p_data.boxs + p_data.pots[0].x - 1.f, at + p_data.pots[0].y, ps.uWidth - p_data.boxs + p_data.pots[1].x - 1.f, at + p_data.pots[1].y, 3.f);
		}
		else if ((ps.dwState & state_checked) != 0) {
			ps.hCanvas->DrawLine(p_data.hBrush, ps.uWidth - p_data.boxs + p_data.pots[2].x - 1.f, at + p_data.pots[2].y, ps.uWidth - p_data.boxs + p_data.pots[3].x - 1.f, at + p_data.pots[3].y, 3.f, 0, TRUE);
			ps.hCanvas->DrawLine(p_data.hBrush, ps.uWidth - p_data.boxs + p_data.pots[4].x - 1.f, at + p_data.pots[4].y, ps.uWidth - p_data.boxs + p_data.pots[3].x - 1.f, at + p_data.pots[3].y, 3.f, 0, TRUE);
		}
		ps.hCanvas->DrawTextByColor(ps.hFont, label, ps.dwTextFormat, ps.rcText.left, ps.rcText.top, ps.rcText.right - p_data.boxs - 5, ps.rcText.bottom, color_text);
	}
}
