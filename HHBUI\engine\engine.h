﻿/**
** =====================================================================================
**
**       文件名称: engine.h
**       创建时间: 2025-08-03 (优化版本)
**       文件描述: 【HHBUI】引擎核心管理系统 - 现代化C++17引擎架构框架 （声明文件）
**
**       主要功能:
**       - 现代化引擎生命周期管理
**       - 智能DPI感知与缩放系统
**       - 高性能资源管理与初始化
**       - 异常安全的引擎状态管理
**       - 跨平台兼容性支持
**       - 调试与性能监控集成
**
**       技术特性:
**       - 采用现代C++17标准与RAII管理
**       - 异常安全保证与错误恢复机制
**       - 智能指针与自动资源管理
**       - 高性能DPI缩放算法
**       - 线程安全的状态管理
**       - 实时性能监控与调试诊断
**
** =====================================================================================
**/

#pragma once
#include <memory>
#include <string>
#include <string_view>
#include <chrono>
#include <optional>
#include <functional>
#include <atomic>
#include <mutex>
#include "application/config.h"

namespace HHBUI
{
	/// 引擎状态枚举
	enum class EngineState : uint8_t
	{
		UNINITIALIZED = 0,
		INITIALIZING,
		INITIALIZED,
		SHUTTING_DOWN,
		ERROR_STATE
	};

	/// 渲染设备类型
	enum class RenderDeviceType : uint8_t
	{
		AUTO_SELECT = 0,
		HARDWARE_ACCELERATED,
		SOFTWARE_FALLBACK,
		HYBRID_MODE
	};

	/// 字体样式枚举（现代化）
	enum class FontStyle : uint32_t
	{
		NORMAL = 0,
		BOLD = 1,
		ITALIC = 2,
		UNDERLINE = 4,
		STRIKEOUT = 8
	};

	/// 现代化引擎初始化配置
	struct EngineInitConfig
	{
		// 渲染设备配置
		RenderDeviceType device_type = RenderDeviceType::AUTO_SELECT;
		int32_t device_index = -1;  // -1表示自动选择

		// 应用程序实例
		HINSTANCE app_instance = nullptr;

		// DPI配置
		float custom_dpi_scale = 0.0f;  // 0表示使用系统DPI
		bool enable_per_monitor_dpi = true;

		// 调试配置
		bool debug_mode = false;
		bool enable_performance_monitoring = false;

		// 字体配置
		std::wstring default_font_family = L"";  // 空字符串表示使用系统默认
		int32_t default_font_size = 14;
		FontStyle default_font_style = FontStyle::NORMAL;

		// 回调函数
		std::function<void(const std::string&)> error_callback = nullptr;
		std::function<void(const std::string&)> debug_callback = nullptr;

		// 验证配置有效性
		[[nodiscard]] bool IsValid() const noexcept;
	};

	/// 引擎性能统计信息
	struct EngineStats
	{
		std::chrono::steady_clock::time_point init_time;
		std::chrono::milliseconds uptime{ 0 };
		float current_dpi_scale = 1.0f;
		uint64_t total_memory_usage = 0;
		uint32_t active_windows = 0;
		uint32_t active_controls = 0;
		float average_frame_time = 0.0f;
	};

	/// 现代化UI引擎核心类
	class TOAPI UIEngine
	{
	public:
		// 静态类，禁用实例化
		UIEngine() = delete;
		~UIEngine() = delete;
		UIEngine(const UIEngine&) = delete;
		UIEngine& operator=(const UIEngine&) = delete;

		/// 初始化引擎
		/// @param config 初始化配置，nullptr表示使用默认配置
		/// @return 初始化结果
		[[nodiscard]] static HRESULT Initialize(const EngineInitConfig* config = nullptr) noexcept;

		/// 关闭引擎
		/// @return 关闭结果
		[[nodiscard]] static HRESULT Shutdown() noexcept;

		/// 获取引擎状态
		/// @return 当前引擎状态
		[[nodiscard]] static EngineState GetState() noexcept;

		/// 检查引擎是否已初始化
		/// @return true如果引擎已初始化
		[[nodiscard]] static bool IsInitialized() noexcept;

		/// 检查是否为调试模式
		/// @return true如果为调试模式
		[[nodiscard]] static bool IsDebugMode() noexcept;

		/// 计算DPI缩放值
		/// @param value 原始值
		/// @return 缩放后的值
		[[nodiscard]] static float ScaleValue(float value) noexcept;

		/// 获取当前DPI缩放系数
		/// @return DPI缩放系数
		[[nodiscard]] static float GetDPIScale() noexcept;

		/// 获取引擎运行时间（秒）
		/// @return 运行时间
		[[nodiscard]] static float GetUptime() noexcept;

		/// 获取引擎版本信息
		/// @return 版本字符串
		[[nodiscard]] static std::wstring_view GetVersion() noexcept;

		/// 获取引擎性能统计
		/// @return 性能统计信息
		[[nodiscard]] static EngineStats GetStats() noexcept;

		/// 设置错误回调函数
		/// @param callback 错误回调函数
		static void SetErrorCallback(std::function<void(const std::string&)> callback) noexcept;

		/// 设置调试回调函数
		/// @param callback 调试回调函数
		static void SetDebugCallback(std::function<void(const std::string&)> callback) noexcept;

		/// 强制垃圾回收
		static void ForceGarbageCollection() noexcept;

		/// 重置性能统计
		static void ResetStats() noexcept;

		// 兼容性方法（保持向后兼容）
		[[deprecated("Use Initialize() instead")]]
		static HRESULT Init(struct info_Init* info = nullptr);

		[[deprecated("Use Shutdown() instead")]]
		static HRESULT UnInit();

		[[deprecated("Use IsDebugMode() instead")]]
		static BOOL QueryDebug();

		[[deprecated("Use IsInitialized() instead")]]
		static BOOL QueryInit();

		[[deprecated("Use ScaleValue() instead")]]
		static FLOAT fScale(FLOAT n);

		[[deprecated("Use GetDPIScale() instead")]]
		static FLOAT GetDefaultScale();

		[[deprecated("Use GetUptime() instead")]]
		static FLOAT GetTime();

		[[deprecated("Use GetVersion() instead")]]
		static LPCWSTR GetVersionLegacy();

	private:
		static std::atomic<EngineState> s_engine_state;
		static std::mutex s_state_mutex;
		static std::chrono::steady_clock::time_point s_init_time;
		static EngineInitConfig s_current_config;
		static EngineStats s_stats;
		static std::function<void(const std::string&)> s_error_callback;
		static std::function<void(const std::string&)> s_debug_callback;

		// 内部辅助方法
		static HRESULT InitializeDPI(const EngineInitConfig& config) noexcept;
		static HRESULT InitializeGraphics(const EngineInitConfig& config) noexcept;
		static HRESULT InitializeFonts(const EngineInitConfig& config) noexcept;
		static void UpdateStats() noexcept;
		static void LogError(const std::string& message) noexcept;
		static void LogDebug(const std::string& message) noexcept;
	};

	// 兼容性结构体（保持向后兼容）
	struct info_Init
	{
		int device = -1;
		HINSTANCE hInstance = nullptr;
		FLOAT dwScaledpi = 0.0f;
		BOOL dwDebug = FALSE;
		LPCWSTR default_font_Face = nullptr;
		INT default_font_Size = 14;
		DWORD default_font_Style = 0;
	};

} // namespace HHBUI
