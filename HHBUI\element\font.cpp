﻿#include "pch.h"
#include "font.h"
#include <dwrite.h>
#include <common/winapi.h>
#include <common/res_pool.h>
#include "font_pool.h"
#include <common/Exception.h>

HHBUI::UIFont::UIFont(LOGFONTW* info)
{
	if (!info)
		info = UIWinApi::ToList.drawing_default_fontLogFont;
    m_context = ExFontPoolD2D::Instance()->CreateFontInfo(info);
}

HHBUI::UIFont::UIFont(LPCWSTR lpwzFontFace, INT dwFontSize, DWORD dwFontStyle)
{
	auto lpLogFont = new LOGFONTW();
	if (lpLogFont != nullptr)
	{
		RtlMoveMemory(lpLogFont, UIWinApi::ToList.drawing_default_fontLogFont, sizeof(LOGFONT));
		if (lpwzFontFace)
		{
			size_t i = lstrlenW(lpwzFontFace);
			if (i > 0)
			{
				RtlMoveMemory((LPVOID)lpLogFont->lfFaceName, lpwzFontFace, i * 2 + 2);
			}
		}
		if (dwFontSize == 0)
			lpLogFont->lfHeight = UIWinApi::ToList.drawing_default_fontLogFont->lfHeight;
		else
			lpLogFont->lfHeight = -UIEngine::ScaleValue(dwFontSize);

		if (dwFontStyle != 0)
		{
			lpLogFont->lfWeight = ((dwFontStyle & FONT_STYLE_BOLD) == 0 ? 400 : 700);
			lpLogFont->lfItalic = ((dwFontStyle & FONT_STYLE_ITALIC) == 0 ? 0 : 1);
			lpLogFont->lfUnderline = ((dwFontStyle & FONT_STYLE_UNDERLINE) == 0 ? 0 : 1);
			lpLogFont->lfStrikeOut = ((dwFontStyle & FONT_STYLE_STRIKEOUT) == 0 ? 0 : 1);
		}
		m_context = ExFontPoolD2D::Instance()->CreateFontInfo(lpLogFont);
	}
}

HHBUI::UIFont::~UIFont()
{
	auto font_context = (ExFontContextD2D*)m_context;
	ExFontPoolD2D::Instance()->DestroyFont(font_context->atom);
}

BOOL HHBUI::UIFont::LoadFromMem(LPVOID data, size_t size, LPCWSTR lpwzFontFace)
{
	if (!size)
		return false;
	ExFontFileContextD2D* tmp = nullptr;
	tmp = ExFontPoolD2D::Instance()->LoadFontFile(data, size, lpwzFontFace);
	return tmp != 0;
}

LPVOID HHBUI::UIFont::GetContext(INT index)
{
	auto font_context = (ExFontContextD2D*)m_context;
	return_if_false(font_context, {}, nullptr);

	switch (index)
	{
	case 0: return font_context->font;
	case 1: return (void*)font_context->LogFont;
	case 2: return (void*)font_context;
	default: return nullptr;
	}
}

INT HHBUI::UIFont::GetSize()
{
	auto font_context = (ExFontContextD2D*)m_context;
	handle_if_false(font_context, EE_NOREADY, L"获取字体信息失败");
	return abs(font_context->LogFont->lfHeight);
}

BOOL HHBUI::UIFont::GetLogFont(LOGFONTW* lpLogFont)
{
	auto font_context = (ExFontContextD2D*)m_context;
	return_if_false(font_context, {}, false);
	if (lpLogFont != 0)
	{
		RtlMoveMemory(lpLogFont, font_context->LogFont, sizeof(LOGFONTW));
		return true;
	}
	return false;
}

BOOL HHBUI::UIFont::GetDefaultLogFontA(LOGFONTA* lpLogFont)
{
	if (UIWinApi::ToList.drawing_default_fontLogFont)
	{
		LPCWSTR lpszFontfamily = UIWinApi::ToList.drawing_default_fontLogFont->lfFaceName;
		if (lstrlenW(lpszFontfamily) > 0)
		{
			RtlMoveMemory((LPVOID)&lpLogFont->lfFaceName, lpszFontfamily, lstrlenW(lpszFontfamily) * 2 + 2);
		}
		lpLogFont->lfHeight = UIWinApi::ToList.drawing_default_fontLogFont->lfHeight;
		lpLogFont->lfCharSet = UIWinApi::ToList.drawing_default_fontLogFont->lfCharSet;
		lpLogFont->lfWeight = UIWinApi::ToList.drawing_default_fontLogFont->lfWeight;
		lpLogFont->lfClipPrecision = UIWinApi::ToList.drawing_default_fontLogFont->lfClipPrecision;
		lpLogFont->lfItalic = UIWinApi::ToList.drawing_default_fontLogFont->lfItalic;
		lpLogFont->lfEscapement = UIWinApi::ToList.drawing_default_fontLogFont->lfEscapement;
		lpLogFont->lfQuality = UIWinApi::ToList.drawing_default_fontLogFont->lfQuality;
		lpLogFont->lfUnderline = UIWinApi::ToList.drawing_default_fontLogFont->lfUnderline;
		return true;
	}
	return false;
}

BOOL HHBUI::UIFont::GetDefaultLogFontW(LOGFONTW* lpLogFont)
{
	if (UIWinApi::ToList.drawing_default_fontLogFont)
	{
		lpLogFont = UIWinApi::ToList.drawing_default_fontLogFont;
		return true;
	}
	return false;
}

