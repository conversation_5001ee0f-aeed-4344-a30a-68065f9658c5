﻿#include "pch.h"
#include "listview.h"
#include <common/winapi.h>
#include <common/memory.h>


HHBUI::UIListView::UIListView(UIBase *hParent, INT x, INT y, INT width, INT height, LPCTSTR lpClsname, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat, BOOL fwScale)
{
	InitSubControl(hParent, x, y, width, height, lpClsname, 0, dwStyle, dwStyleEx, nID, dwTextFormat);
    p_data.cr_select = new UIBrush(UIColor(30, 30, 30, 255));
    p_data.pots[0].x = UIEngine::ScaleValue(16) * 0.2f;
    p_data.pots[1].x = UIEngine::ScaleValue(16) * 0.8f;
    p_data.pots[0].y = p_data.pots[1].y = UIEngine::ScaleValue(16) / 2.f;

    p_data.pots[2].x = UIEngine::ScaleValue(16) * 0.2f;
    p_data.pots[2].y = UIEngine::ScaleValue(16) * 0.5f;
    p_data.pots[3].x = UIEngine::ScaleValue(16) * 0.4f;
    p_data.pots[3].y = UIEngine::ScaleValue(16) * 0.8f;
    p_data.pots[4].x = UIEngine::ScaleValue(16) * 0.8f;
    p_data.pots[4].y = UIEngine::ScaleValue(16) * 0.3f;

    SetItemWidth((m_data.Frame.right - m_data.Frame.left) / UIWinApi::ToList.drawing_default_dpi);
}

INT HHBUI::UIListView::GetHittest()
{
    return p_data.nHittest;
}

void HHBUI::UIListView::SetItemCount(INT nCount, LPARAM lParam)
{
     lv_setitemcount(nCount, lParam);
}

void HHBUI::UIListView::SetItemWidth(INT nWidth)
{
    if (nWidth > 0)
    {
        p_data.width_item = nWidth;
        p_data.width_spitem = nWidth;
    }
}

void HHBUI::UIListView::SetItemHeight(INT nHeight)
{
    p_data.height_item = nHeight;
}

void HHBUI::UIListView::SetItemSplitWidth(INT nWidth)
{
    p_data.width_split = nWidth;
}

void HHBUI::UIListView::SetItemSplitHeight(INT nHeight)
{
    p_data.height_split = nHeight;
}

void HHBUI::UIListView::SetEnsureVisible(INT iItem)
{
    lv_showitem(iItem, TRUE);
}

void HHBUI::UIListView::UpdateIndex(INT start_iItem, INT end_iItem)
{
    if (end_iItem == 0)
        end_iItem = start_iItem + 1;
    for (INT i = start_iItem; i < end_iItem; i++)
    {
        lv_redrawitem(i);
    }
}

INT HHBUI::UIListView::GetItemState(INT iItem)
{
    INT mstate = 0;
    if (lv_checkitem(iItem))
    {
        mstate = lv_getitemstate(p_data.lpItems, iItem);
    }
    return mstate;
}

void HHBUI::UIListView::SetItemState(INT iItem, INT fstate)
{
    lv_setitemstate(iItem, fstate);
}

INT HHBUI::UIListView::GetCountPerpage()
{
    return p_data.count_view;
}

BOOL HHBUI::UIListView::GetItemRect(INT iItem, ExRectF& lpRc)
{
    if (lv_checkitem(iItem))
    {
        lv_rectfromiitem(iItem, FLAGS_CHECK(m_data.dwStyle, eos_elvs_horizontallist), lpRc);
        return TRUE;
    }
    return FALSE;
}

INT HHBUI::UIListView::GetSelect()
{
    return p_data.index_select;
}

void HHBUI::UIListView::SetSelect(INT iItem, BOOL bShow)
{
    lv_reselect(iItem, bShow);
}

INT HHBUI::UIListView::GetTopIndex()
{
    return p_data.index_start;
}

INT HHBUI::UIListView::GetHotItem()
{
    return p_data.index_mouse;
}

LRESULT HHBUI::UIListView::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_SIZE)
	{
		lv_size(lParam);
	}
    else if (uMsg == WM_DESTROY)
    {
        delete p_data.cr_select;
    }
    else if (uMsg == WM_MOUSELEAVE)
    {
        lv_mouseleave();
    }
    else if (uMsg == WM_MOUSEMOVE)
    {
        lv_mousemove(hWnd, wParam, lParam);
    }
    else if (uMsg >= WM_KEYFIRST && uMsg <= WM_KEYLAST) //256,264
    {
        BOOL bSingleSel = FLAGS_CHECK(m_data.dwStyle, eos_elvs_allowmultiple);
        if (bSingleSel)
        {
            INT iKey = m_data.pWnd->GetKeys();
            if (iKey == 1 && wParam == 0x41)//A键
            {
                lv_reselect(-1, TRUE);
            }

        }
    }
    else if (uMsg == WM_LBUTTONUP)
    {
        INT nCount = p_data.count_items;
        if (nCount > 0)
        {
            p_data.down_x = 0;
            p_data.down_y = 0;
            DispatchNotify(WMM_LVN_ITEMDRAGDROP_END, wParam, lParam);
        }
    }
    else if (uMsg == WM_LBUTTONDOWN)
    {
        lv_btndown(uMsg, wParam, lParam);
    }
    else if (uMsg == WM_VSCROLL || uMsg == WM_HSCROLL)
    {
        lv_onvscrollbar(uMsg, wParam, lParam);
    }
    return OnPsProc(hWnd, uMsg, wParam, lParam);
}

int calcItemW(int pWidth, int ibWidth, int inWidth, int padding, int iCount) {
    int nw = (float)(pWidth - padding) / (inWidth + padding);
    if (iCount < nw) return ibWidth;
    if (((ibWidth + padding) * (nw + 1) + padding) <= pWidth) nw++;
    return (float)(pWidth - padding) / nw - padding;
}
void HHBUI::UIListView::lv_size(LPARAM lParam)
{
    if (lParam == 0) return;
    BOOL bHView = FLAGS_CHECK(m_data.dwStyle, eos_elvs_horizontallist);
    if (p_data.width_item == 0)
    {
        FLOAT iWidth = GET_X_LPARAM(lParam) - 1;
        FLOAT iHeight = UIEngine::ScaleValue(25);
        //项目尺寸
        p_data.width_item = iWidth;
        p_data.width_spitem = iWidth;
        p_data.height_item = iHeight;
    }
    else if (p_data.width_item != GET_X_LPARAM(lParam))
    {
        INT width_item = calcItemW(GET_X_LPARAM(lParam) / UIWinApi::ToList.drawing_default_dpi, p_data.width_spitem, p_data.width_item, p_data.width_split, p_data.count_items);
        if (width_item > 0)
            p_data.width_item = width_item;
    }
    if (p_data.width_spec == 0)
    {
        //特殊悬浮项尺寸
        p_data.width_spec = p_data.width_item;
        p_data.height_spec = p_data.height_item;
    }
    if (p_data.width_split == 0)
    {
        //间隔尺寸
        p_data.width_split = 1;
        p_data.height_split = 1;
    }
    lv_updatesbvalue();
    lv_updateviewindex(bHView);
}

void HHBUI::UIListView::lv_updatesbvalue()
{
    auto hHSB = (UIScroll*)m_data.objHScroll;
    auto hVSB = (UIScroll*)m_data.objVScroll;
    INT width = m_data.Frame_c.right - m_data.Frame_c.left;
    INT height = m_data.Frame_c.bottom - m_data.Frame_c.top;
    BOOL bHView = FLAGS_CHECK(m_data.dwStyle, eos_elvs_horizontallist);
    INT nCount = p_data.count_items;
    INT iWidth = UIEngine::ScaleValue(p_data.width_item + p_data.width_split);
    INT iHeight = UIEngine::ScaleValue(p_data.height_item + p_data.height_split);
    INT nVS = 0;
    INT nHS = 0;
    if (bHView)
    {
        nVS = height / iHeight;
        if (nVS == 0)
            nVS = 1;
        nHS = (INT)(width / iWidth) + 1;
    }
    else
    {
        nHS = width / iWidth;
        if (nHS == 0)
            nHS = 1;
        nVS = (INT)(height / iHeight) + 1;
    }
    INT vWidth = 0;
    INT vHeight = 0;
    if (bHView) //横向排列
    {
        vWidth = (nCount / nVS + (nCount % nVS == 0 ? 0 : 1)) * iWidth;
        if (hHSB != 0)
        {
            if (vWidth > width)
            {
                if (nVS > 1)
                {
                    if (height < nVS * iHeight)
                    {
                        nVS = nVS - 1;
                    }
                    vWidth = (nCount / nVS + (nCount % nVS == 0 ? 0 : 1)) * iWidth;
                }
            }
        }
        if (vWidth < width)
        {
            vWidth = width;
        }
    }
    else
    {
        vHeight = (nCount / nHS + (nCount % nHS == 0 ? 0 : 1)) * iHeight;
        if (vHeight > height)
        {
            //横向数量大于1时，不管有没纵向滚动条都需要加上判断，超过的话强行-1
            if (nHS > 1)
            {
                if (width < nHS * iWidth)
                {
                    nHS = nHS - 1;
                }
                vHeight = (nCount / nHS + (nCount % nHS == 0 ? 0 : 1)) * iHeight;
            }
        }
        vWidth = nHS * iWidth - 1;
    }
    p_data.count_view_h = nHS;
    p_data.count_view_v = nVS;
    p_data.count_view = nVS * nHS;
    p_data.width_view = vWidth;
    p_data.height_view = vHeight;
    if (hVSB != 0)
    {
        SetScrollShow(false, vHeight - height > 0);
        SetScrollInfo(false, SIF_PAGE | SIF_RANGE, 0, vHeight - height, height, 0, FALSE);
    }
    if (hHSB != 0)
    {
        SetScrollShow(true, vWidth - width > 1);
        SetScrollInfo(true, SIF_PAGE | SIF_RANGE, 0, vWidth - width, width, 0, FALSE);
    }
}

void HHBUI::UIListView::lv_updateviewindex(BOOL bHView)
{
    INT nWidth = UIEngine::ScaleValue(p_data.width_item + p_data.width_split);
    INT nHeight = UIEngine::ScaleValue(p_data.height_item + p_data.height_split);
    INT nOffset = GetScrollPos(bHView);
    INT iStart = 0;
    INT iEnd = 0;
    if (bHView)
    {
        if (nOffset != p_data.nOffsetX)
        {
            p_data.nOffsetX = nOffset;
        }
        iStart = (nOffset / nWidth) * p_data.count_view_v + 1;
        iEnd = iStart + p_data.count_view - 1;
        if (((iEnd - 1) / p_data.count_view_v) * nWidth - nOffset < m_data.Frame_c.right - m_data.Frame_c.left)
        {
            iEnd = iEnd + p_data.count_view_v;
        }
        
    }
    else
    {
        if (nOffset != p_data.nOffsetY)
        {
            p_data.nOffsetY = nOffset;
        }
        iStart = (nOffset / nHeight) * p_data.count_view_h + 1;
        iEnd = iStart + p_data.count_view - 1;
        if (((iEnd - 1) / p_data.count_view_h) * nHeight - nOffset < m_data.Frame_c.bottom - m_data.Frame_c.top)
        {
            iEnd = iEnd + p_data.count_view_h;
        }
    }
    p_data.index_start = iStart;
    p_data.index_end = iEnd;
}

void HHBUI::UIListView::OnPaintProc(ps_context ps)
{
    INT iStart = p_data.index_start;
    INT iEnd = p_data.index_end;
    BOOL bHView = FLAGS_CHECK(m_data.dwStyle, eos_elvs_horizontallist);

    BOOL Clip = OnPsDraw(ps);
    for (INT i = iStart; i < iEnd; i++)
    {
        if (lv_checkitem_view(i))
        {
            ExRectF rcItem{};
            lv_rectfromiitem(i, bHView, rcItem);
            ExRectF rcClip{};
            if (rcClip.IntersectRect(ps.rcPaint, rcItem))
            {
                lv_drawitem(ps, i, rcClip, rcItem);
            }
        }
    }
    if (Clip)
        ps.hCanvas->ResetClip();
}

BOOL HHBUI::UIListView::lv_checkitem_view(INT iItem)
{
    BOOL ret = FALSE;
    if (lv_checkitem(iItem))
    {
        ret = iItem >= p_data.index_start && iItem <= p_data.index_end;
    }
    return ret;
}

BOOL HHBUI::UIListView::lv_checkitem(INT iItem)
{
    return iItem > 0 && p_data.count_items >= iItem;
}

void HHBUI::UIListView::lv_rectfromiitem(INT iItem, BOOL bHView, ExRectF& rcItem)
{
    INT i = iItem - 1;
    INT nWidth = UIEngine::ScaleValue(p_data.width_item + p_data.width_split);
    INT nHeight = UIEngine::ScaleValue(p_data.height_item + p_data.height_split);
    if (bHView)
    {
        rcItem.left = m_data.Frame_c.left + (i / p_data.count_view_v) * nWidth - p_data.nOffsetX;
        rcItem.top = m_data.Frame_c.top + (i % p_data.count_view_v) * nHeight - p_data.nOffsetY;
    }
    else
    {
        rcItem.left = m_data.Frame_c.left + (i % p_data.count_view_h) * nWidth - p_data.nOffsetX;
        rcItem.top = m_data.Frame_c.top + (i / p_data.count_view_h) * nHeight - p_data.nOffsetY;
    }
    rcItem.right = rcItem.left + UIEngine::ScaleValue(p_data.width_item);
    rcItem.bottom = rcItem.top + UIEngine::ScaleValue(p_data.height_item);
}

void HHBUI::UIListView::lv_drawitem(ps_context ps, INT iItem, ExRectF rcClip, ExRectF rcItem)
{
    INT atomRect = 0;
    ps_customdraw ecd{};
    ecd.hCanvas = ps.hCanvas;
    ecd.dwState = lv_getitemstate(p_data.lpItems, iItem);
    ecd.dwStyle = ps.dwStyle;
    ecd.dwTextFormat = ps.dwTextFormat;
    ecd.hFont = ps.hFont;
    ecd.iItem = iItem;
    ecd.dpi = ps.dpi;
    //ecd.iItemParam = 0;
    ecd.rcPaint = rcItem;
    ecd.rcText = ps.rcText;
    ecd.uWidth = ps.uWidth;
    ecd.uHeight = ps.uHeight;
    BOOL bSingleSel = FLAGS_CHECK(m_data.dwStyle, eos_elvs_allowmultiple);
    if (OnPsCustomDraw(ecd.iItem, ecd) == false && SendMsgProc(WMM_EX_CUSTOMDRAW, iItem, (size_t)&ecd) == S_OK)
    {
        p_data.cr_select->SetColor(UIColor(30, 30, 30, 255));
        BOOL bSelect = false;
        if (ecd.dwState & state_select)
        {
            p_data.cr_select->SetColor(UIColor(0, 153, 255, 255));
            bSelect = TRUE;
        }
        else if (ecd.dwState & state_hover)
        {
            p_data.cr_select->SetColor(UIColor(Brown));
        }
        if (bSingleSel && (ecd.dwStyle & eos_elvs_showcheck) != eos_elvs_showcheck)
        {
            auto ret = ExRectF(ecd.rcPaint.left + UIEngine::ScaleValue(5), ecd.rcPaint.top + (ecd.rcPaint.bottom - ecd.rcPaint.top - UIEngine::ScaleValue(16)) / 2, UIEngine::ScaleValue(16), UIEngine::ScaleValue(16), TRUE);
            if (bSelect)
            {
                ps.hCanvas->FillRoundRect(p_data.cr_select, ret.left, ret.top, ret.right, ret.bottom, UIEngine::ScaleValue(2));
                p_data.cr_select->SetColor(UIColor(255, 255, 255, 255));
                ps.hCanvas->DrawLine(p_data.cr_select, ret.left + p_data.pots[2].x, ret.top + p_data.pots[2].y, ret.left + p_data.pots[3].x, ret.top + p_data.pots[3].y, 3, 0, TRUE);
                ps.hCanvas->DrawLine(p_data.cr_select, ret.left + p_data.pots[4].x, ret.top + p_data.pots[4].y, ret.left + p_data.pots[3].x, ret.top + p_data.pots[3].y, 3, 0, TRUE);
            }
            else
                ps.hCanvas->DrawRoundRect(p_data.cr_select, ret.left, ret.top, ret.right, ret.bottom, UIEngine::ScaleValue(2), 1.f);
        }
    }
}

INT HHBUI::UIListView::lv_getitemstate(LPVOID lpItems, INT iItem)
{
    return __get_int(lpItems, (iItem - 1) * 4);
}

void HHBUI::UIListView::lv_mouseleave()
{
    LPVOID lpItems = p_data.lpItems;
    INT iLast = p_data.index_mouse;
    if (lv_checkitem(iLast))
    {
        lv_item_changestate(lpItems, iLast, state_hover, TRUE, 0, 0, 0);
        p_data.index_mouse = 0;
    }
}

void HHBUI::UIListView::lv_mousemove(HWND hWnd, WPARAM wParam, LPARAM lParam)
{
    INT x = GET_X_LPARAM(lParam);
    INT y = GET_Y_LPARAM(lParam);
    LPVOID lpItems = p_data.lpItems;
    INT iSelect = p_data.index_select;
    INT iLast = p_data.index_mouse;
    BOOL vLast = lv_checkitem(iLast);
    INT ox = 0;
    INT oy = 0;
    INT iCur = lv_itemfrompos(x, y, ox, oy);
    BOOL vCur = lv_checkitem(iCur);
    INT iHitTest = LVHT_NOWHERE;
    BOOL bSingelSelect = !FLAGS_CHECK(m_data.dwStyle, eos_elvs_allowmultiple);
    BOOL bDragdrop = FLAGS_CHECK(m_data.dwStyleEx, eos_ex_dragdrop);
    BOOL bShowAllwasy = FLAGS_CHECK(m_data.dwStyle, eos_table_showtable);
    if (wParam != 1 || bSingelSelect && !bShowAllwasy)
    {
        if (bDragdrop && iSelect != 0 && p_data.down_x != 0)
        {
            DispatchNotify(WMM_LVN_ITEMDRAGDROP_BEGIN, iCur, lParam);
        }
        if (FLAGS_CHECK(m_data.dwStyle, eos_elvs_itemtracking))
        {
            if (iCur != iSelect)
            {
                if (vCur || !bShowAllwasy)
                {
                    if (iSelect != 0)
                    {
                        lv_item_changestate(lpItems, iSelect, state_select, TRUE, 0, 0, 0);
                    }
                    p_data.index_select = iCur;
                }
                if (vCur)
                {
                    if (lv_queryitemstate(lpItems, iCur, state_disable))
                    {
                        iCur = 0;
                    }
                    else
                    {
                        //lv_item_changestate(hWnd, lpItems, iCur, state_select, FALSE, WMM_LVN_HOTTRACK, iCur, iSelect);
                    }
                }
                DispatchNotify(WMM_LVN_ITEMCHANGED, iCur, iSelect);
            }
        }
        else
        {
            if (iCur != iLast)
            {
                if (vLast)
                {
                    lv_item_changestate(lpItems, iLast, state_hover, TRUE, 0, 0, 0);
                }
                if (vCur)
                {
                    if (lv_queryitemstate(lpItems, iCur, state_disable))
                    {
                        iCur = 0;
                    }
                    else if (!lv_queryitemstate(lpItems, iCur, state_select))
                    {
                        lv_item_changestate(lpItems, iCur, state_hover, FALSE, 0, 0, 0);
                    }
                }
            }
        }

    }
    p_data.index_mouse = iCur;
}

void HHBUI::UIListView::lv_item_changestate(LPVOID lpItems, INT iItem, INT state, BOOL bRemove, INT nEvent, WPARAM wParam, LPARAM lParam)
{
    lv_setitemstate(lpItems, iItem, state, bRemove);
    lv_redrawitem(iItem);
    if (nEvent != 0)
    {
        DispatchNotify(nEvent, wParam, lParam);
    }
}

void HHBUI::UIListView::lv_setitemstate(LPVOID lpItems, INT iItem, INT dwState, BOOL bRemove)
{
    INT offset = (iItem - 1) * 4;
    if (bRemove)
    {
        __del(lpItems, offset, dwState);
    }
    else
    {
        __add(lpItems, offset, dwState);
    }
}

void HHBUI::UIListView::lv_redrawitem(INT iItem)
{
    if (lv_checkitem_view(iItem))
    {
        ExRectF rcItem{};
        lv_rectfromiitem(iItem, FLAGS_CHECK(m_data.dwStyle, eos_elvs_horizontallist), rcItem);
        Redraw(rcItem);
    }
}

INT HHBUI::UIListView::lv_itemfrompos(INT x, INT y, INT& offsetPosX, INT& offsetPosY)
{
    offsetPosX = 0;
    offsetPosY = 0;
    INT uItem = 0;
    if (m_data.Frame_c.PtInRect(x,y))
    {
        INT nWidth = UIEngine::ScaleValue(p_data.width_item + p_data.width_split);
        INT nHeight = UIEngine::ScaleValue(p_data.height_item + p_data.height_split);
        INT realleft = 0;
        INT offsety = 0;
        INT offsetx = 0;
        INT realtop = 0;
        if (FLAGS_CHECK(m_data.dwStyle, eos_elvs_horizontallist))
        {
            realleft = x - m_data.Frame_c.left + p_data.nOffsetX;
            offsetx = realleft % nWidth;
            if (offsetx <= UIEngine::ScaleValue(p_data.width_item))
            {
                realtop = y - m_data.Frame_c.top + p_data.nOffsetY;
                offsety = realtop % nHeight;
                if (offsety <= UIEngine::ScaleValue(p_data.height_item))
                {
                    INT tmp = realtop / nHeight;
                    if (tmp < p_data.count_view_v)
                    {
                        uItem = (realleft / nWidth) * p_data.count_view_v + tmp + 1;
                        if (uItem > p_data.count_items)
                        {
                            uItem = 0;
                        }
                        else
                        {
                            offsetPosX = offsetx;
                            offsetPosY = offsety;
                        }
                    }
                }
            }
        }
        else
        {
            realtop = y - m_data.Frame_c.top + p_data.nOffsetY;
            offsety = realtop % nHeight;
            if (offsety <= UIEngine::ScaleValue(p_data.height_item))
            {
                realleft = x - m_data.Frame_c.left + p_data.nOffsetX;
                offsetx = realleft % nWidth;
                if (offsetx <= UIEngine::ScaleValue(p_data.width_item))
                {
                    INT tmp = realleft / nWidth;
                    if (tmp < p_data.count_view_h)
                    {
                        uItem = (realtop / nHeight) * p_data.count_view_h + tmp + 1;
                        if (uItem > p_data.count_items)
                        {
                            uItem = 0;
                        }
                        else
                        {
                            offsetPosX = offsetx;
                            offsetPosY = offsety;
                        }
                    }
                }
            }
        }
    }
    return uItem;
}

BOOL HHBUI::UIListView::lv_queryitemstate(LPVOID lpItems, INT iItem, INT dwState)
{
    return __query(lpItems, (iItem - 1) * 4, dwState);
}

size_t HHBUI::UIListView::lv_reselect(INT iItem, BOOL bShow)
{
    INT nCount = p_data.count_items;
    LPVOID lpItems = p_data.lpItems;
    if (nCount > 0 && iItem == -1)
    {
        for (INT i = 1; i < nCount + 1; i++)
        {
            if (!lv_queryitemstate(lpItems, i, state_select))
            {

                lv_item_changestate(lpItems, i, state_select, FALSE, 0, 0, 0);
                //p_data.count_selects_ = p_data.count_selects_ + 1;
            }
        }
    }
    else if (nCount > 0)
    {
        for (INT i = 1; i < nCount + 1; i++)
        {
            if (lv_queryitemstate(lpItems, i, state_select))
            {

                lv_item_changestate(lpItems, i, state_select, TRUE, 0, 0, 0);
            }
        }
        p_data.count_selects = 0;
    }
    p_data.index_select = iItem;

    if (iItem >= 0)
    {
        if (lv_checkitem(iItem))
        {
            nCount = p_data.index_select;
            p_data.count_selects = 1;
            //lv_item_changestate(lpItems, iItem, state_select, FALSE, WMM_LVN_ITEMCHANGED, iItem, nCount);
            if (bShow)
            {
                lv_showitem(iItem, FALSE);
            }
        }
    }
    return 0;
}

BOOL HHBUI::UIListView::lv_showitem(INT iItem, BOOL bCheck)
{
    if (bCheck)
    {
        if (!lv_checkitem(iItem))
        {
            return FALSE;
        }
    }

    BOOL bHView = FLAGS_CHECK(m_data.dwStyle, eos_elvs_horizontallist);
    ExRectF rcItem{};
    lv_rectfromiitem(iItem, bHView, rcItem);
    rcItem.Offset(-m_data.Frame_c.left, -m_data.Frame_c.top);
    INT oPos = 0;
    INT nPage = 0;
    INT nLine = 0;
    INT nView = 0;
    lv_getscrollbarvalue(bHView, oPos, nLine, nPage, nView);
    INT nPos = 0;
    if (bHView)
    {
        if (rcItem.left < 0)
        {
            nPos = oPos + rcItem.left;
        }
        else
        {
            if (rcItem.right > nPage)
            {
                nPos = oPos + rcItem.right - nPage + p_data.width_split;
            }
            else
            {
                return FALSE;
            }
        }
    }
    else
    {
        if (rcItem.top < 0)
        {
            nPos = oPos + rcItem.top;
        }
        else
        {
            if (rcItem.bottom > nPage)
            {
                nPos = oPos + rcItem.bottom - nPage + p_data.height_split;
            }
            else
            {
                return FALSE;
            }
        }
    }
    nPos = lv_checkpos(nPos, nView, nPage);
    if (bHView)
    {
        p_data.nOffsetX = nPos;
    }
    else
    {
        p_data.nOffsetY = nPos;
    }
    SetScrollPos(bHView, nPos, TRUE);
    lv_updateviewindex(bHView);
    Redraw();
    return TRUE;
}

void HHBUI::UIListView::lv_getscrollbarvalue(BOOL bHSB, INT& oPos, INT& nLine, INT& nPage, INT& nView)
{
    if (bHSB)
    {
        oPos = p_data.nOffsetX;
        nPage = m_data.Frame_c.right - m_data.Frame_c.left;
        nLine = UIEngine::ScaleValue(p_data.width_item + p_data.width_split);
        nView = p_data.width_view;
    }
    else
    {
        oPos = p_data.nOffsetY;
        nPage = m_data.Frame_c.bottom - m_data.Frame_c.top;
        nLine = UIEngine::ScaleValue(p_data.height_item + p_data.height_split);
        nView = p_data.height_view;
    }
}

INT HHBUI::UIListView::lv_checkpos(INT nPos, INT nView, INT nPage)
{
    if (nPos > nView - nPage)
    {
        nPos = nView - nPage;
    }
    if (nPos < 0)
        nPos = 0;
    return nPos;
}

void HHBUI::UIListView::lv_btndown(INT uMsg, size_t wParram, LPARAM lParam)
{
    INT nCount = p_data.count_items;//项目总数
    if (nCount > 0)
    {
        INT x = GET_X_LPARAM(lParam);
        INT y = GET_Y_LPARAM(lParam);
        p_data.down_x = x;
        p_data.down_y = y;
        LPVOID lpItems = p_data.lpItems;// 所有表项状态指针,每个表项占4字节 
        INT ox = 0;
        INT oy = 0;
        //当前鼠标项目索引
        INT iCur = lv_itemfrompos(x, y, ox, oy);
        //当前鼠标是否有效按下项目
        BOOL vCur = lv_checkitem(iCur);
        if (vCur)
        {
            if (lv_queryitemstate(lpItems, iCur, state_disable))
            {
                return;//表项是禁止状态 
            }
        }
        p_data.index_down = iCur;

        //记录的"当前选中"索引
        INT iSelect = p_data.index_select;
        //记录的"当前选中"索引是否有效
        BOOL vSelect = lv_checkitem(iSelect);
        //是否单选风格,假为多选 真为单选
        BOOL bSingleSel = !((m_data.dwStyle & eos_elvs_allowmultiple) == eos_elvs_allowmultiple);
        BOOL bShowAllwasy = ((m_data.dwStyle & eos_table_showtable) == eos_table_showtable);
        INT iKey = m_data.pWnd->GetKeys();// 1 VK_CONTROL   2 VK_SHIFT
        //--------------------------------------------------------------------
        if (bSingleSel)//单选风格且当前鼠标有效悬浮项目   选中该项目
        {
            if (iCur != iSelect)
            {
                if (vSelect)//取消记录选中的表项状态
                {
                    if (vCur)//bShowAllwasy默认时为假:单击空白处 取消选中
                    {
                        lv_item_changestate(lpItems, iSelect, state_select, TRUE, WMM_LVN_ITEMSELECTC, iCur, iSelect);
                    }
                    else
                    {
                        iCur = iSelect;
                    }
                }
                if (vCur)//当前鼠标按下表项有效
                {
                    //选中当前鼠标悬浮的表项
                    lv_item_changestate(lpItems, iCur, state_select, FALSE, WMM_LVN_ITEMSELECTD, iCur, iSelect);
                    DispatchNotify(WMM_LVN_ITEMCHANGED, iCur, iSelect);
                }
                p_data.index_select = iCur;
                p_data.count_selects = 1;
            }
        }
        else//多选风格
        {
            INT& nSelects = p_data.count_selects;
            // ctrl shift 都没有按下, (左键单击) 或 (左键双击) 
            if ((iKey & 1) == 0 && (iKey & 2) == 0)
            {    // 命中检查框
                BOOL bHView = FLAGS_CHECK(m_data.dwStyle, eos_elvs_horizontallist);
                ExRectF lpRc{};
                lv_rectfromiitem(iCur, bHView, lpRc);
                ExRectF rc{ 0.f,lpRc.top, (float)p_data.height_item ,lpRc.top + (float)p_data.height_item };
                BOOL checkboxok = rc.PtInRect(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));

                if (!bShowAllwasy && checkboxok && !FLAGS_CHECK(m_data.dwStyle, eos_elvs_showcheck))//检查框
                {
                    lv_itemselectone(uMsg, lpItems, nCount, iCur, iSelect);
                }
                else if (FLAGS_CHECK(m_data.dwStyle, eos_elvs_showcheck) && p_data.fctrl)//只有取消默认选择框的时候ctrl未按住全部取消选择
                {
                    lv_itemselectzero(uMsg, lpItems, nCount, iCur, iSelect);
                }
                p_data.fctrl = FALSE;
            }
            else if ((iKey & 1) != 0 && (iKey & 2) == 0 && vCur) //ctrl按下 shift没有按下
            {
                if (!vSelect)//一个都未选中
                {
                    p_data.index_select = iCur;
                }
                bool is_select = lv_queryitemstate(lpItems, iCur, state_select);
                if (is_select)
                {
                    lv_item_changestate(lpItems, iCur, state_select, TRUE, WMM_LVN_ITEMSELECTC, iCur, iSelect);
                    nSelects--;
                }
                else
                {
                    lv_item_changestate(lpItems, iCur, state_select, FALSE, WMM_LVN_ITEMSELECTD, iCur, iSelect);
                    nSelects++;
                }
                p_data.fctrl = TRUE;
            }
            else if ((iKey & 1) == 0 && (iKey & 2) != 0 && vCur) //ctrl没有按下 shift按下
            {
                if (!vSelect)//一个都未选中
                {
                    lv_item_changestate(lpItems, iCur, state_select, FALSE, WMM_LVN_ITEMSELECTD, iCur, iSelect);
                    p_data.index_select = iCur;
                    nSelects = 1;//选中项目数
                }
                else
                {
                    nSelects = 0;//选中项目数
                    for (INT i = 1; i < nCount + 1; i++)
                    {
                        bool inside = p_data.index_select <= iCur ?
                            (p_data.index_select <= i && i <= iCur)
                            : (iCur <= i && i <= p_data.index_select);
                        if (inside)
                        {
                            if (!lv_queryitemstate(lpItems, i, state_select))
                            {
                                lv_item_changestate(lpItems, i, state_select, FALSE, WMM_LVN_ITEMSELECTD, i, iSelect);
                            }
                            nSelects++;
                        }
                        else
                        {
                            if (lv_queryitemstate(lpItems, i, state_select))
                            {
                                lv_item_changestate(lpItems, i, state_select, TRUE, WMM_LVN_ITEMSELECTC, i, iSelect);
                            }
                        }
                    }
                }
                p_data.fctrl = TRUE;
            }
        }
        Redraw();
    }
}

void HHBUI::UIListView::lv_itemselectone(INT uMsg, LPVOID lpItems, INT nCount, INT iCur, INT iSelect)
{
    /*
   for (INT i = 1; i <= nCount; i++)
   {
       if (_lv_queryitemstate(lpItems, i, state_select) && i != iCur)//如果单选为已经选中的,则跳过
       {
           _lv_item_changestate(hWnd, parent, pObj, pOwner, lpItems, i, state_select, TRUE, WMM_LVN_ITEMSELECTC, i, iSelect);
       }
   }
   */
    lv_item_changestate(lpItems, iCur, state_select, lv_queryitemstate(lpItems, iCur, state_select), WMM_LVN_ITEMSELECTC, iCur, iSelect);
    DispatchNotify(WMM_LVN_ITEMCHANGED, iCur, iSelect);

    p_data.count_selects = 1;
    p_data.index_select = iCur;
}

void HHBUI::UIListView::lv_itemselectzero(INT uMsg, LPVOID lpItems, INT nCount, INT iCur, INT iSelect)
{
    for (INT i = 1; i <= nCount; i++)
    {
        if (lv_queryitemstate(lpItems, i, state_select))
        {
            lv_item_changestate(lpItems, i, state_select, TRUE, WMM_LVN_ITEMSELECTC, i, iSelect);
        }
    }
    p_data.count_selects = 0;
    p_data.index_select = 0;
}

void HHBUI::UIListView::lv_onvscrollbar(INT uMsg, WPARAM wParam, LPARAM lParam)
{
    INT nCode = GET_X_LPARAM(wParam);
    BOOL bHView = FLAGS_CHECK(m_data.dwStyle, eos_elvs_horizontallist);
    BOOL bHScoll = (uMsg == WM_HSCROLL);
    INT oPos = 0;
    INT nPage = 0;
    INT nLine = 0;
    INT nView = 0;
    lv_getscrollbarvalue(bHScoll, oPos, nLine, nPage, nView);
    INT nPos = 0;
    if (nCode == SB_THUMBPOSITION)
    {
        nPos = GetScrollTrackPos(bHScoll);
    }
    else
    {
        if (nCode == SB_PAGEUP)
        {
            nPos = oPos - nPage;
        }
        else if (nCode == SB_PAGEDOWN)
        {
            nPos = oPos + nPage;
        }
        else if (nCode == SB_LINEUP)
        {
            nPos = oPos - nLine;
        }
        else if (nCode == SB_LINEDOWN)
        {
            nPos = oPos + nLine;
        }
        else if (nCode == SB_TOP)
        {
            nPos = 0;
        }
        else if (nCode == SB_BOTTOM)
        {
            nPos = nView - nPage;
        }
        else
        {
            return;
        }
    }
    nPos = lv_checkpos(nPos, nView, nPage);

    if (nPos != oPos)
    {
        if (bHScoll)
        {
            p_data.nOffsetX = nPos;
        }
        else
        {
            p_data.nOffsetY = nPos;
        }
        SetScrollPos(bHScoll, nPos, TRUE);
        lv_updateviewindex(bHView);
        Redraw();
    }
}

void HHBUI::UIListView::lv_setitemcount(INT nCount, LPARAM lParam)
{
    BOOL bHView = FLAGS_CHECK(m_data.dwStyle, eos_elvs_horizontallist);
    LPVOID pOld = p_data.lpItems;
    if (pOld != 0)
    {
        ExMemFree(pOld);
    }
    //取 提交的项目索引和当前项目索引
    int index = HIWORD(lParam);
    int index_select = p_data.index_select;
    if (nCount == 0)
    {
        index = 0;
        index_select = 0;
    }
    pOld = ExMemAlloc(nCount * 4);
    p_data.lpItems = pOld;
    p_data.count_items = nCount;
    p_data.index_select = index_select;
    p_data.index_mouse = index_select;
    p_data.index_track_start = 0;
    p_data.index_track_end = 0;
   
    p_data.count_selects = 0;
    lv_updatesbvalue();

    INT nPosX = 0;
    INT nPosY = 0;
    INT nPage = 0;
    INT nView = 0;
    INT nLine = 0;
    if ((GET_X_LPARAM(lParam) & LVSICF_NOSCROLL) != 0)
    {
        lv_getscrollbarvalue(TRUE, nPosX, nLine, nPage, nView);
        nPosX = lv_checkpos(nPosX, nView, nPage);
        lv_getscrollbarvalue(FALSE, nPosY, nLine, nPage, nView);
        nPosY = lv_checkpos(nPosY, nView, nPage);
    }
    else
    {
        nPosX = 0;
        nPosY = 0;
    }
    p_data.nOffsetX = nPosX;
    p_data.nOffsetY = nPosY;
    SetScrollPos(TRUE, nPosX, TRUE);
    SetScrollPos(FALSE, nPosY, TRUE);
    lv_updateviewindex(bHView);

    Redraw();
    //判断是否需要选中项目
    if (nCount > 0 && index > 0)
    {
        if (index >= nCount)
            lv_reselect(index, TRUE);
    }
}

void HHBUI::UIListView::lv_setitemstate(INT iItem, INT dwState)
{
    if (lv_checkitem(iItem))
    {
        if (iItem == p_data.index_select)
        {
            if ((dwState & state_select) == 0)
            {
                p_data.index_select = 0;
            }
        }
        LPVOID lpItems = p_data.lpItems;
        __set_int(lpItems, (iItem - 1) * 4, dwState);
        lv_redrawitem(iItem);
    }
}


