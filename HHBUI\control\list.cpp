﻿#include "pch.h"
#include "list.h"
void HHBUI::UIList::OnCreate()
{
	s_data.hBrush = new UIBrush();//创建一个热点颜色画刷
	s_data.pArray = new UIarray();
}
bool HHBUI::UIList::IndexCheck(INT index)
{
	if (index <= (INT)s_data.pArray->size() && index >= 1)
		return true;
	return false;
}

INT HHBUI::UIList::AddItem(LPCWSTR pwzText, UIImage *nImage, INT index, bool draw)
{
	auto pItemInfo = new ListItem();
	pItemInfo->text = pwzText;
	pItemInfo->nImage = nImage;
	return AddItem(pItemInfo, index, draw);
}

INT HHBUI::UIList::AddItem(ListItem* item, INT index, bool draw)
{
	if (item)
	{
		if (item->text != 0)
			item->text = StrDupW(item->text);
		if (index == -1)
		{
			s_data.pArray->insert((size_t)item);
		}
		else
		{
			s_data.pArray->insert((size_t)item, index);
			//列表发生改变 需要更新当前选中项目的索引
			if (draw)
				UpdateIndex(index);
		}
	}
	return (INT)s_data.pArray->size();
}

void HHBUI::UIList::SetItem(INT index, LPCWSTR pwzText, UIImage *nImage, bool draw)
{
	if (IndexCheck(index))
	{
		auto pItemInfo = new ListItem();
		auto itemText = ((ListItem*)s_data.pArray->get(index))->text;
		if (pwzText == NULL)
		{
			if (itemText)
			{
				pItemInfo->text = StrDupW(itemText);
				LocalFree((LPVOID)itemText);
			}
		}
		else
		{
			if (itemText)
				LocalFree((LPVOID)itemText);
			pItemInfo->text = StrDupW(pwzText);
		}
		if (!nImage)
		{
			pItemInfo->nImage = ((ListItem*)s_data.pArray->get(index))->nImage;
		}
		else
		{
			pItemInfo->nImage = nImage;
		}
		s_data.pArray->set(index, (size_t)pItemInfo);

		if (draw)
			UpdateIndex(index);
	}
}

INT HHBUI::UIList::DeleteItem(INT index, bool draw)
{
	if (index == -1)
	{
		index = (INT)s_data.pArray->size() - 1;
		if (index < 0) index = 0;
	}
	if (IndexCheck(index))
	{
		auto item = (ListItem*)s_data.pArray->get(index);
		if (item->nImage) {
			delete item->nImage;
			item->nImage = nullptr;
		}
		if (item->text) {
			LocalFree((LPVOID)item->text);
			item->text = nullptr;
		}
		delete item;
		s_data.pArray->erase(index);
		//列表发生改变 需要更新当前选中项目的索引
		UpdateIndex(index);
	}
	return (INT)s_data.pArray->size();
}


void HHBUI::UIList::DeleteAllItem(bool draw)
{
	for (size_t i = 1; i <= s_data.pArray->size(); ++i) {
		auto item = (ListItem*)s_data.pArray->get(i);
		// 删除图像资源 外部创建的内存由外部管理释放
		/*
		if (item->nImage) {
			delete item->nImage;
			item->nImage = nullptr;
		}*/
		if (item->text) {
			LocalFree((LPVOID)item->text);
			item->text = nullptr;
		}
	}

	s_data.pArray->clear();
	if (draw)
		Update();
}


void HHBUI::UIList::Update()
{
	SetItemCount((INT)s_data.pArray->size());
	Redraw();
}

void HHBUI::UIList::SetCrHot(UIColor hover, UIColor checked, UIColor down)
{
	if (!hover.empty())
		s_data.Color[0] = hover;
	if (!checked.empty())
		s_data.Color[1] = checked;
	if (!down.empty())
		s_data.Color[2] = down;
}

BOOL HHBUI::UIList::GetItem(int index, ListItem** item)
{
	if (index == -1 && !s_data.pArray->empty())
	{
		*item = (ListItem*)s_data.pArray->end();
	}
	else if (IndexCheck(index))
	{
		*item = (ListItem*)s_data.pArray->get(index);
	}
	else
	{
		*item = nullptr; // 或者采取其他适当的错误处理方式
		return FALSE;
	}
	return TRUE;
}

INT HHBUI::UIList::GetItemCount()
{
	return (INT)s_data.pArray->size();
}

LRESULT HHBUI::UIList::OnPsProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_SIZE)
	{
	}
	else if (uMsg == WM_DESTROY)
	{
		if (!s_data.isData)
		{
			DeleteAllItem(false);
			delete s_data.pArray;
		}
		delete s_data.hBrush;
	}
	return S_OK;
}

BOOL HHBUI::UIList::OnPsCustomDraw(INT iItem, ps_customdraw ps)
{
	BOOL bSingleSel = (ps.dwStyle & eos_elvs_allowmultiple);
	BOOL bshowcheck = (ps.dwStyle & eos_elvs_showcheck);
	BOOL bHView = (ps.dwStyle & eos_elvs_horizontallist);
	UINT nWidthcheckbutton = 0, nWidthIcon = 0, nHeightIcon = 0;
	auto pItemInfo = (ListItem*)s_data.pArray->get(iItem);
	if (bSingleSel && !bshowcheck)
	{
		nWidthcheckbutton = UIEngine::ScaleValue(16);
	}
	if (pItemInfo->nImage != 0) //如果有图标？
	{
		pItemInfo->nImage->GetSize(nWidthIcon, nHeightIcon);
		nWidthIcon += nWidthcheckbutton + UIEngine::ScaleValue(13);
	}
	else {

		nWidthIcon = nWidthcheckbutton + UIEngine::ScaleValue(13);
	}
	UIColor crlot;
	GetColor(color_text_normal, crlot);
	BOOL reDraw = FALSE;
	if (bshowcheck && p_data.fctrl)
	{
		if (ps.dwState & state_select)
		{
			GetColor(color_text_down, crlot);
			s_data.hBrush->SetColor(s_data.Color[1]);
			reDraw = TRUE;
		}
		else if (ps.dwState & state_hover)
		{
			GetColor(color_text_hover, crlot);
			s_data.hBrush->SetColor(s_data.Color[0]);
			reDraw = TRUE;
		}
	}
	else
	{
		if (iItem == p_data.index_down)
		{
			GetColor(color_text_down, crlot);
			s_data.hBrush->SetColor(s_data.Color[2]);
			reDraw = TRUE;
		}
		else if (iItem == p_data.index_mouse)
		{
			GetColor(color_text_hover, crlot);
			s_data.hBrush->SetColor(s_data.Color[0]);
			reDraw = TRUE;
		}
		else if (iItem == p_data.index_select)
		{
			GetColor(color_text_hover, crlot);
			s_data.hBrush->SetColor(s_data.Color[1]);
			reDraw = TRUE;
		}
		
	}
	if (reDraw)
		ps.hCanvas->FillRect(s_data.hBrush, ps.rcPaint.left, ps.rcPaint.top, ps.rcPaint.right, ps.rcPaint.bottom);

	if (pItemInfo->nImage != 0) //如果有图标？
	{
		auto rcIcon = ps.rcPaint;
		if (pItemInfo->text)
		{
			rcIcon.left = ps.rcPaint.left + nWidthcheckbutton + 5;
			rcIcon.top = ps.rcPaint.top;
			rcIcon.right = rcIcon.left + nWidthIcon + nWidthcheckbutton;
			rcIcon.bottom = ps.rcPaint.bottom;
			auto nWidth = rcIcon.right - rcIcon.left;
			auto nHeight = rcIcon.bottom - rcIcon.top;
			ps.hCanvas->DrawImage(pItemInfo->nImage, rcIcon.left + (nWidth - nWidthIcon) / 2, rcIcon.top + (nHeight - static_cast<float>(nHeightIcon)) / 2);
		}
		else {
			auto nWidth = rcIcon.right - rcIcon.left;
			auto nHeight = rcIcon.bottom - rcIcon.top;
			ps.hCanvas->DrawImageRect(pItemInfo->nImage, rcIcon.left + (nWidth - nWidthIcon) / 2, rcIcon.top + (nHeight - static_cast<float>(nHeightIcon)) / 2, rcIcon.left + (nWidth - static_cast<float>(nWidthIcon)) / 2 + nWidthIcon, rcIcon.top + (nHeight - static_cast<float>(nHeightIcon)) / 2 + nHeightIcon);
		}
	}
	if (pItemInfo->text)
	{
		auto ret = ExRectF(ps.rcPaint.left + nWidthIcon, ps.rcPaint.top, ps.rcPaint.right - nWidthIcon, ps.rcPaint.bottom);
		ps.hCanvas->DrawTextByColor(ps.hFont, pItemInfo->text, ps.dwTextFormat, ret.left, ret.top, ret.right, ret.bottom, crlot);
	}
	return FALSE;
}



