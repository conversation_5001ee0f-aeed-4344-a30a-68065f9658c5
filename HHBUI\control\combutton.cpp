﻿#include "pch.h"
#include "combutton.h"
#include <common/winapi.h>
#include "loading_internal.h"

HHBUI::UICombutton::UICombutton(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-combutton", lpszName, dwStyle, dwStyleEx, nID, dwTextFormat);
	p_data.hBrush = new UIBrush();
	p_data.hBrush_Bkg = new UIBrush();
	SetCircleTessellationMaxError(0.30f);
}

HHBUI::UICombutton::~UICombutton()
{
}

void HHBUI::UICombutton::SetIcon(HICON dwicon, BOOL fCalcSize)
{
	if (p_data.hicon)
		delete p_data.hicon;
	p_data.hicon = new UIImage(dwicon);
	if (fCalcSize)
	{
		p_data.hicon->GetSize(p_data.hiconsize.width, p_data.hiconsize.height);
		p_data.hiconsize.width = UIEngine::ScaleValue(p_data.hiconsize.width);
		p_data.hiconsize.height = UIEngine::ScaleValue(p_data.hiconsize.height);
	}
}
void HHBUI::UICombutton::SetCrbutton(UIColor normal, UIColor hover, UIColor down)
{
	if (!normal.empty())
		p_data.crbutton_[0] = normal;
	if (!hover.empty())
		p_data.crbutton_[1] = hover;
	if (!down.empty())
		p_data.crbutton_[2] = down;
}
void HHBUI::UICombutton::GetCrbutton(UIColor& normal, UIColor& hover, UIColor& down)
{
	normal = p_data.crbutton_[0];
	hover = p_data.crbutton_[1];
	down = p_data.crbutton_[2];
}
void HHBUI::UICombutton::SetCrBkgbutton(UIColor normal, UIColor hover, UIColor down)
{
	if (!normal.empty())
		p_data.crBkgbutton_[0] = normal;
	if (!hover.empty())
		p_data.crBkgbutton_[1] = hover;
	if (!down.empty())
		p_data.crBkgbutton_[2] = down;
	Redraw();
}
void HHBUI::UICombutton::GetCrBkgbutton(UIColor& normal, UIColor& hover, UIColor& down)
{
	normal = p_data.crBkgbutton_[0];
	hover = p_data.crBkgbutton_[1];
	down = p_data.crBkgbutton_[2];
}
void HHBUI::UICombutton::SetImgbutton(UIImage *img1, UIImage *img2)
{
	if (img1)
	{
		if (p_data.imgbutton[0])
			delete p_data.imgbutton[0];
		p_data.imgbutton[0] = img1;
	
	}
	if (img2)
	{
		if (p_data.imgbutton[1])
			delete p_data.imgbutton[1];
		p_data.imgbutton[1] = img2;
	
	}
}
void HHBUI::UICombutton::Update()
{
	if (((m_data.dwStyle & UISTYLE_TITLE) == UISTYLE_TITLE))
	{
		ExRectF tmp{};
		GetRect(tmp);
		remove(tmp.right - tmp.left, tmp.bottom - tmp.top);
		Redraw();
	}
}
void HHBUI::UICombutton::Clear()
{
	if (((m_data.dwStyle & UISTYLE_TITLE) == UISTYLE_TITLE))
	{
		auto sObj = (UIControl*)m_objChildFirst;
		while (sObj)
		{
			auto nextObj = (UIControl*)sObj->GetNode(GW_HWNDNEXT);
			delete sObj;
			sObj = nextObj;
		}

	}
}
INT HHBUI::UICombutton::Reset()
{
	INT nMinWidth = 0;
	if (((m_data.dwStyle & UISTYLE_TITLE) == UISTYLE_TITLE))
	{
		ExRectF rcCaption{};
		GetRect(rcCaption);
		std::vector<INT> aryBtn = { UISTYLE_BTN_CLOSE, UISTYLE_BTN_MAX, UISTYLE_BTN_MIN, UISTYLE_BTN_MENU, UISTYLE_BTN_SKIN, UISTYLE_BTN_SETTING, UISTYLE_BTN_HELP };
		INT left = rcCaption.right - rcCaption.left;
		DWORD UIStyle = m_data.pWnd->UIStyle();

		for (INT i = 0; i < 7; i++)
		{
			if ((UIStyle & aryBtn[i]) != 0)
			{
				RECT rcObject{ 0 };
				rcObject.left = 0;
				rcObject.top = 0;
				rcObject.right = UIEngine::ScaleValue(35);
				rcObject.bottom = UIEngine::ScaleValue(35);


				left = left - rcObject.right;
				auto objTmp = new UICombutton(this, left, 0, rcObject.right, rcObject.bottom, NULL, aryBtn[i] | eos_textoffset | eos_nodpiscale, 0, aryBtn[i], -1);
				nMinWidth += rcObject.right;
			}
		}
	}
	return nMinWidth;
}
HICON _wnd_geticonhandle(HWND hWnd, BOOL isbigicon)
{
	HICON ret = (HICON)SendMessageW(hWnd, WM_GETICON, (isbigicon ? 1 : 0), 0);
	if (ret == 0)
	{
		ret = (HICON)GetClassLongPtrW(hWnd, isbigicon ? GCLP_HICON : GCLP_HICONSM);
	}
	return ret;
}

LRESULT HHBUI::UICombutton::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_NCCREATE)//当控件第一次被创建时，此消息在WM_CREATE消息发送前发送
	{
		LPCWSTR ret = nullptr;
		if (((m_data.dwStyle & UISTYLE_BTN_MIN) == UISTYLE_BTN_MIN))
		{
			ret = L"最小化";
		}
		else if (((m_data.dwStyle & UISTYLE_BTN_CLOSE) == UISTYLE_BTN_CLOSE))
		{
			ret = L"关闭";
		}
		else if (((m_data.dwStyle & UISTYLE_BTN_HELP) == UISTYLE_BTN_HELP))
		{
			ret = L"帮助";
		}

		if (ret != 0)
			m_data.pstrTips = StrDupW(ret);
	}
	else if (uMsg == WM_CREATE)
	{
		m_data.dwFlags |=  EOF_NOBORDER;
		if (m_data.dwTextFormat == 0)
		{
			m_data.dwTextFormat = DT_VCENTER | DT_LEFT | DT_SINGLELINE | DT_WORD_ELLIPSIS;
		}
		if ((m_data.dwStyle & UISTYLE_TITLE) != UISTYLE_TITLE)
		{
			p_data.crbutton_[0] = UIColor(0, 0, 0, 255);
			p_data.crbutton_[1] = UIColor(130, 130, 130, 255);
			p_data.crbutton_[2] = UIColor(30, 30, 30, 255);
			p_data.crBkgbutton_[1] = UIColor(194, 195, 201, 155);
			p_data.crBkgbutton_[2] = UIColor(194, 195, 201, 155);

		}
		if ((m_data.pWnd->UIStyle() & UISTYLE_HASICON) == UISTYLE_HASICON)
		{
			HICON hicon = (HICON)_wnd_geticonhandle(hWnd, FALSE);
			if (hicon == 0)
			{
				hicon = (HICON)_wnd_geticonhandle((HWND)m_data.pWnd->GetlParam(), FALSE);
			}
			if (hicon != 0)
			{
				p_data.hicon = new UIImage(hicon);
				p_data.hiconsize.width = UIEngine::ScaleValue(16);
				p_data.hiconsize.height = UIEngine::ScaleValue(16);

			}
		}
	}
	else if (uMsg == WM_NCHITTEST)
	{
		INT ret = m_data.dwStyle;
		if ((ret & UISTYLE_TITLE) != 0)
		{
			if ((ret & UISTYLE_HASICON) != 0)
			{
				ret = HTCAPTION;
			}
			else
			{
				ret = HTCAPTION;
			}
			return ret;
		}
	}
	else if (uMsg == WM_MOUSEHOVER)
	{
		LPCWSTR ret = nullptr;
		if ((m_data.dwStyle & UISTYLE_TITLE) != UISTYLE_TITLE)
		{
			SetState(state_hover, FALSE);
			if (((m_data.dwStyle & UISTYLE_BTN_MAX) == UISTYLE_BTN_MAX))
			{
				LocalFree((HLOCAL)m_data.pstrTips);
				if ((GetWindowLongPtrW(hWnd, GWL_STYLE) & WS_MAXIMIZE) != 0)
				{
					ret = StrDupW(L"还原");
				}
				else
				{
					ret = StrDupW(L"最大化");
				}
				m_data.pstrTips = ret;
			}
			else if (((m_data.dwStyle & UISTYLE_BTN_MIN) == UISTYLE_BTN_MIN))
			{
				LocalFree((HLOCAL)m_data.pstrTips);
				if ((GetWindowLongPtrW(hWnd, GWL_STYLE) & WS_MINIMIZE) != 0)
				{
					ret = StrDupW(L"还原");
				}
				else
				{
					ret = StrDupW(L"最小化");
				}
				m_data.pstrTips = ret;
			}
			else if (((m_data.dwStyle & UISTYLE_BTN_MENU) == UISTYLE_BTN_MENU))
			{
				LocalFree((HLOCAL)m_data.pstrTips);
				m_data.pstrTips = StrDupW(L"菜单");
			}
			else if (((m_data.dwStyle & UISTYLE_BTN_SKIN) == UISTYLE_BTN_SKIN))
			{
				LocalFree((HLOCAL)m_data.pstrTips);
				m_data.pstrTips = StrDupW(L"主题");
			}
			else if (((m_data.dwStyle & UISTYLE_BTN_SETTING) == UISTYLE_BTN_SETTING))
			{
				LocalFree((HLOCAL)m_data.pstrTips);
				m_data.pstrTips = StrDupW(L"设置");
			}
			Redraw();
		}
	}
	else if (uMsg == WM_MOUSELEAVE)
	{
		if (!p_data.imgbutton[0])
		{
			p_data.start = 0.f;
		}
		if (!((m_data.dwStyle & UISTYLE_TITLE) == UISTYLE_TITLE))
		{
			SetState(state_hover | state_down, TRUE);
			Redraw();
		}
	}
	else if (uMsg == WM_LBUTTONDOWN || uMsg == WM_RBUTTONDOWN)
	{
		if (!p_data.imgbutton[0])
		{
			p_data.start = 0.f;
		}
		if (!((m_data.dwStyle & UISTYLE_TITLE) == UISTYLE_TITLE))
		{
			SetState(state_down, FALSE);
			Redraw();
		}
	}
	else if (uMsg == WM_LBUTTONUP || uMsg == WM_RBUTTONUP)
	{
		if (!((m_data.dwStyle & UISTYLE_TITLE) == UISTYLE_TITLE))
		{
			SetState(state_down, TRUE);
			Redraw();
		}
	}
	else if (uMsg == WM_TIMER)
	{
		/*
		if (wParam == 0)
			p_data.start = 0.f;
		else
			p_data.start = UIEngine::GetTime();
	

		Redraw();
		*/
	}
	else if (uMsg == WM_EX_LCLICK)
	{
		if ((m_data.dwStyle & UISTYLE_BTN_CLOSE) != 0)
		{
			if (((m_data.pWnd->UIStyle() & EWS_MESSAGEBOX) == EWS_MESSAGEBOX) || ((m_data.pWnd->Flags() & EWF_BMODAL) == EWF_BMODAL))
			{
				EndDialog(hWnd, IDCANCEL);
			}
			else
			{
				PostMessageW(hWnd, WM_SYSCOMMAND, SC_CLOSE, 0);
			}
		}
		else if ((m_data.dwStyle & UISTYLE_BTN_MAX) != 0)
		{
			if ((GetWindowLongPtrW(hWnd, GWL_STYLE) & WS_MAXIMIZE) != 0)
			{
				SendMessageW(hWnd, WM_SYSCOMMAND, SC_RESTORE, 0);
			}
			else
			{
				SendMessageW(hWnd, WM_SYSCOMMAND, SC_MAXIMIZE, 0);
			}
		}
		else if ((m_data.dwStyle & UISTYLE_BTN_MIN) != 0)
		{
			if ((GetWindowLongPtrW(hWnd, GWL_STYLE) & WS_MINIMIZE) != 0)
			{
				SendMessageW(hWnd, WM_SYSCOMMAND, SC_RESTORE, 0);
			}
			else
			{
				SendMessageW(hWnd, WM_SYSCOMMAND, SC_MINIMIZE, 0);
			}
		}
		SetState(state_hover | state_down, TRUE);
		Redraw();
	}
	else if (uMsg == WM_SIZE)
	{
		if (((m_data.dwStyle & UISTYLE_TITLE) == UISTYLE_TITLE))
			remove(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
	}
	else if (uMsg == WM_DESTROY)
	{
		if (p_data.hicon)
			delete p_data.hicon;
		if (p_data.imgbutton[0])
			delete p_data.imgbutton[0];
		if (p_data.imgbutton[1])
			delete p_data.imgbutton[1];
		delete p_data.hBrush;
		delete p_data.hBrush_Bkg;
	}
	return S_OK;
}


void HHBUI::UICombutton::remove(INT width, INT height)
{
	auto sObj = (UICombutton*)m_objChildFirst;
	BOOL bReCalced = FALSE;
	INT nOffxset = 0, nOffyset = 0;
	while (sObj)
	{
		ExRectF tmp{};
		sObj->GetRect(tmp, 0, TRUE);
		if (!bReCalced)
		{
			bReCalced = TRUE;
			nOffxset = width - tmp.right;
			sObj->SetRadius(0, m_data.pWnd->GetRadius(), 0, 0);
		}
		sObj->SetPos(tmp.left + nOffxset, tmp.top + nOffyset, 0, 0, 0, SWP_NOSIZE | SWP_NOZORDER | SWP_NOREDRAW | SWP_NOACTIVATE | SWP_EX_NODPISCALE);
		sObj = (UICombutton*)sObj->GetNode(GW_HWNDNEXT);
	}
}

void HHBUI::UICombutton::OnPaintProc(ps_context ps)
{
	p_data.hBrush->SetColor(p_data.crbutton_[0]);
	if ((ps.dwStyle & UISTYLE_TITLE) != UISTYLE_TITLE)
	{
		p_data.hBrush_Bkg->SetColor(p_data.crBkgbutton_[0]);
		if ((ps.dwState & state_down) != 0)
		{
			p_data.hBrush_Bkg->SetColor(p_data.crBkgbutton_[2]);
			p_data.hBrush->SetColor(p_data.crbutton_[2]);
		}
		else if ((ps.dwState & state_hover) != 0)
		{
			UIColor tmp = p_data.crbutton_[1];
			if (tmp.GetR() != 1.0f && tmp.GetG() != 1.0f && tmp.GetB() != 1.0f)
				tmp.SetColorLights(0.8f);

			if ((ps.dwStyle & UISTYLE_BTN_CLOSE) == UISTYLE_BTN_CLOSE)
			{
				p_data.hBrush_Bkg->SetColor(UIColor(L"#C42B1C"));
				p_data.hBrush->SetColor(UIColor(255, 255, 255));
			}
			else
			{
				p_data.hBrush->SetColor(tmp);
				p_data.hBrush_Bkg->SetColor(p_data.crBkgbutton_[1]);
			}
		}
		ps.hCanvas->FillRect(p_data.hBrush_Bkg, 0.f, 0.f, ps.uWidth, ps.uHeight);

	}


	RECT rcOffset{ 0 };
	if ((ps.dwState & state_down) != 0 && (ps.dwStyle & eos_textoffset) != 0)
		OffsetRect(&rcOffset, 1 * ps.dpi, 1 * ps.dpi);

	if ((ps.dwStyle & UISTYLE_BTN_MAX) != UISTYLE_BTN_MAX)
	{
		if (p_data.imgbutton[0])
		{
			UINT Width = 0, Height = 0;
			p_data.imgbutton[0]->GetSize(Width, Height);
			ps.hCanvas->DrawImage(p_data.imgbutton[0], (ps.uWidth - Width) / 2, (ps.uHeight - Height) / 2);
		}
		else
		{
			if ((ps.dwStyle & UISTYLE_BTN_CLOSE) == UISTYLE_BTN_CLOSE)
			{
				ps.hCanvas->DrawLine(p_data.hBrush, UIEngine::ScaleValue(12.f) + rcOffset.left, UIEngine::ScaleValue(12.f) + rcOffset.top, ps.uWidth - UIEngine::ScaleValue(12.f) + rcOffset.left, ps.uHeight - UIEngine::ScaleValue(12.f) + rcOffset.top, 1.5f);
				ps.hCanvas->DrawLine(p_data.hBrush, ps.uWidth - UIEngine::ScaleValue(12.f) + rcOffset.left, UIEngine::ScaleValue(12.f) + rcOffset.top, UIEngine::ScaleValue(12.f) + rcOffset.left, ps.uHeight - UIEngine::ScaleValue(12.f) + rcOffset.top, 1.5f);
			}
			else if ((ps.dwStyle & UISTYLE_BTN_MIN) != 0)
			{
				ps.hCanvas->DrawLine(p_data.hBrush, UIEngine::ScaleValue(12.f) + rcOffset.left, (ps.uHeight / 2.f + UIEngine::ScaleValue(2.f)) + rcOffset.top,
					ps.uWidth - UIEngine::ScaleValue(12.f) + rcOffset.left, (ps.uHeight / 2.f + UIEngine::ScaleValue(2.f)) + rcOffset.top, 1.5f);
			}
			else if ((ps.dwStyle & UISTYLE_BTN_MENU) == UISTYLE_BTN_MENU)
			{
				// 计算中点位置，用来保持线段长度一致
				float midX = ps.uWidth / 2.f + rcOffset.left * ps.dpi;
				float topY = UIEngine::ScaleValue(14) + rcOffset.top;
				float bottomY = ps.uHeight - UIEngine::ScaleValue(14) + rcOffset.top;

				// 第一条线起始点
				float startX1 = UIEngine::ScaleValue(11) + rcOffset.left;
				// 第二条线起始点
				float startX2 = ps.uWidth - UIEngine::ScaleValue(12) + rcOffset.left;
				ps.hCanvas->DrawLine(p_data.hBrush, startX1, topY, midX, bottomY, 1.5f);
				ps.hCanvas->DrawLine(p_data.hBrush, startX2, topY, midX, bottomY, 1.5f);

			}
			else if ((ps.dwStyle & UISTYLE_BTN_SETTING) == UISTYLE_BTN_SETTING)
			{
				ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
				float radius = UIEngine::ScaleValue(7.f), thickness = UIEngine::ScaleValue(2.5f), speed = 4.8f;
				size_t pins = 8;
				int num_segments = CalcCircleAutoSegmentCount(radius);
				const float start = p_data.start * speed;
				const float bg_angle_offset = PI_2 / num_segments;
				const float bg_radius = radius - thickness;

				for (int i = 0; i <= num_segments; i++)
				{
					const float a = (i * bg_angle_offset);
					ImVec2 p = ImVec2(centre.x + ImCos(a) * bg_radius, centre.y + ImSin(a) * bg_radius);
					ps.hCanvas->DrawPoint(p_data.hBrush, p.x, p.y, bg_radius / 2);
				}

				const float rmin = bg_radius;
				const float rmax = radius;
				const float pin_angle_offset = PI_2 / pins;
				for (size_t i = 0; i <= pins; i++)
				{
					float a = start + (i * pin_angle_offset);
					ps.hCanvas->DrawLine(p_data.hBrush, centre.x + ImCos(a) * rmin, centre.y + ImSin(a) * rmin, centre.x + ImCos(a) * rmax, centre.y + ImSin(a) * rmax, thickness);
				}

			}
			else if ((ps.dwStyle & UISTYLE_BTN_SKIN) == UISTYLE_BTN_SKIN)
			{
				ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
				float radius = UIEngine::ScaleValue(9.f), thickness = UIEngine::ScaleValue(2.5f), speed = 4.8f;
				float start = p_data.start * speed;
				size_t bars = 6;
				start += ease_inoutquad(ImSin(ImFmod(start, IM_PI)));
				thickness += ease_inoutquad(ImSin(ImFmod(start, IM_PI))) * (thickness * 0.5f);

				const float angle_offset = PI_2 / bars;
				const float angle_offset_t = angle_offset * 0.3f;
				bars = ImMin<size_t>(bars, 32);

				const float rmin = radius - thickness - 1;
				auto get_points = [&](float left, float right) -> std::array<D2D1_POINT_2F, 3> {
					return {
					  D2D1::Point2F(centre.x + ImCos(left - 0.1f) * radius, centre.y + ImSin(left - 0.1f) * radius),
					  D2D1::Point2F(centre.x + ImCos(right + 0.15f) * radius, centre.y + ImSin(right + 0.15f) * radius),
					  D2D1::Point2F(centre.x + ImCos(right - 0.91f) * rmin, centre.y + ImSin(right - 0.91f) * rmin)
					};
					};

				auto draw_sectors = [&](float s) {
					for (size_t i = 0; i <= bars; i++) {
						float left = s + (i * angle_offset) - angle_offset_t;
						float right = s + (i * angle_offset) + angle_offset_t;
						auto points = get_points(left, right);
						ps.hCanvas->FillPoly(p_data.hBrush, points.data(), points.size());
					}
					};
				draw_sectors(start);
			}
			else if ((ps.dwStyle & UISTYLE_BTN_HELP) == UISTYLE_BTN_HELP)
			{
				ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
				float radius = UIEngine::ScaleValue(7.f), thickness = 3.f, speed = 4.0f, yang_detlta_r = 0.f, angle = 2.3f;
				int num_segments = CalcCircleAutoSegmentCount(radius);

				const float startI = p_data.start * speed;
				const float startY = p_data.start * (speed + (yang_detlta_r > 0.f ? ImClamp(yang_detlta_r * 0.5f, 0.5f, 2.f) : 0.f));
				const float angle_offset = angle / num_segments;
				const float th = thickness / num_segments;

				for (int i = 0; i < num_segments; i++)
				{
					const float a = startI + (i * angle_offset);
					const float a1 = startI + ((i + 1) * angle_offset);

					ImVec2 x = ImVec2(centre.x + ImCos(a) * radius, centre.y + ImSin(a) * radius);
					ImVec2 y = ImVec2(centre.x + ImCos(a1) * radius, centre.y + ImSin(a1) * radius);

					ps.hCanvas->DrawLine(p_data.hBrush, x.x, x.y, y.x, y.y, th * i);
				}
				const float ai_end = startI + (num_segments * angle_offset);
				ImVec2 circle_i_center{ centre.x + ImCos(ai_end) * radius, centre.y + ImSin(ai_end) * radius };
				ps.hCanvas->DrawPoint(p_data.hBrush, circle_i_center.x, circle_i_center.y, thickness * 1.2f, TRUE);

				const float rv = 1.f;
				const float yang_radius = (radius - yang_detlta_r);
				for (int i = 0; i < num_segments; i++)
				{
					const float a = startY + IM_PI + (i * angle_offset);
					const float a1 = startY + IM_PI + ((i + 1) * angle_offset);

					ImVec2 x = ImVec2(centre.x + ImCos(a * rv) * yang_radius, centre.y + ImSin(a * rv) * yang_radius);
					ImVec2 y = ImVec2(centre.x + ImCos(a1 * rv) * yang_radius, centre.y + ImSin(a1 * rv) * yang_radius);
					ps.hCanvas->DrawLine(p_data.hBrush, x.x, x.y, y.x, y.y, th * i);
				}
				const float ay_end = startY + IM_PI + (num_segments * angle_offset);
				ImVec2 circle_y_center{ centre.x + ImCos(ay_end * rv) * yang_radius, centre.y + ImSin(ay_end * rv) * yang_radius };
				ps.hCanvas->DrawPoint(p_data.hBrush, circle_y_center.x, circle_y_center.y, thickness * 1.2f, TRUE);
			}
		}
	}
	else
	{
		UINT Width = 0, Height = 0;
		if ((GetWindowLongPtrW(GethWnd(), GWL_STYLE) & WS_MAXIMIZE) != 0)
		{
			if (p_data.imgbutton[1])
			{
				p_data.imgbutton[1]->GetSize(Width, Height);
				ps.hCanvas->DrawImage(p_data.imgbutton[1], (ps.uWidth - Width) / 2, (ps.uHeight - Height) / 2);
			}
			else
			{
				ps.hCanvas->DrawRect(p_data.hBrush, 13 * ps.dpi + rcOffset.left, 11 * ps.dpi + rcOffset.top, ps.uWidth - 10 * ps.dpi + rcOffset.left, ps.uHeight - 13 * ps.dpi + rcOffset.top, 1.5f);
				ps.hCanvas->SetClipRect(8 * ps.dpi, 14 * ps.dpi, ps.uWidth - 14 * ps.dpi, ps.uHeight - 10 * ps.dpi);
				if ((ps.dwState & state_hover) != 0)
					ps.hCanvas->Clear(p_data.crBkgbutton_[1]);
				else
					ps.hCanvas->Clear(p_data.crBkgbutton_[0]);
				ps.hCanvas->DrawRect(p_data.hBrush, 10 * ps.dpi + rcOffset.left, 15 * ps.dpi + rcOffset.top, ps.uWidth - 15 * ps.dpi + rcOffset.left, ps.uHeight - 11 * ps.dpi + rcOffset.top, 1.5f);
				ps.hCanvas->ResetClip();
			}

		}
		else
		{
			if (p_data.imgbutton[0])
			{
				p_data.imgbutton[0]->GetSize(Width, Height);
				ps.hCanvas->DrawImage(p_data.imgbutton[0], (ps.uWidth - Width) / 2, (ps.uHeight - Height) / 2);
			}
			else
			{
				ps.hCanvas->DrawRect(p_data.hBrush, UIEngine::ScaleValue(11.f) + rcOffset.left, UIEngine::ScaleValue(11.f) + rcOffset.top, ps.uWidth - UIEngine::ScaleValue(11.f) + rcOffset.left, ps.uHeight - UIEngine::ScaleValue(11.f) + rcOffset.top, 1.5f);
				ps.hCanvas->FillRect(p_data.hBrush, UIEngine::ScaleValue(11.f) + rcOffset.left, UIEngine::ScaleValue(11.f) + rcOffset.top, ps.uWidth - UIEngine::ScaleValue(11.f) + rcOffset.left, UIEngine::ScaleValue(16.f) + rcOffset.top);
			}
		}
	}



	if ((ps.dwStyle & UISTYLE_TITLE) == UISTYLE_TITLE)
	{
		INT left = ps.rcText.left;
		if (p_data.hicon != 0)
		{
			ps.hCanvas->DrawImageRect(p_data.hicon, left, (ps.rcText.bottom - ps.rcText.top - p_data.hiconsize.height) / 2,
				left + p_data.hiconsize.width, (ps.rcText.bottom - ps.rcText.top - p_data.hiconsize.height) / 2 + p_data.hiconsize.height);
			left = left + p_data.hiconsize.width + 4;
		}
		std::wstring wzPostion;
		if (m_data.pstrTitle)
		{
			wzPostion = m_data.pstrTitle;
			if (UIEngine::IsDebugMode())
			{
				wzPostion += L" [DEBUG模式]";
			}
			UIColor yColor;
			GetColor(color_text_normal, yColor);
			ps.hCanvas->DrawTextByColor(ps.hFont, wzPostion.c_str(), ps.dwTextFormat, left, ps.rcText.top, ps.rcText.right, ps.rcText.bottom, yColor);
		}

	}
}

int HHBUI::UICombutton::CalcCircleAutoSegmentCount(float radius)
{
	// Automatic segment count
	const int radius_idx = (int)(radius + 0.999999f); // ceil to never reduce accuracy
	if (radius_idx >= 0 && radius_idx < IM_ARRAYSIZE(p_data.CircleSegmentCounts))
		return p_data.CircleSegmentCounts[radius_idx]; // Use cached value
	else
		return p_data.CircleSegmentCounts[0];
}

void HHBUI::UICombutton::SetCircleTessellationMaxError(float max_error)
{
	if (p_data.CircleSegmentMaxError == max_error)
		return;

	_ASSERT(max_error > 0.0f);
	p_data.CircleSegmentMaxError = max_error;
	for (int i = 0; i < IM_ARRAYSIZE(p_data.CircleSegmentCounts); i++)
	{
		const float radius = (float)i;
		p_data.CircleSegmentCounts[i] = (BYTE)((i > 0) ? IM_DRAWLIST_CIRCLE_AUTO_SEGMENT_CALC(radius, p_data.CircleSegmentMaxError) : IM_DRAWLIST_ARCFAST_SAMPLE_MAX);
	}
}

