﻿/**
** =====================================================================================
**
**       文件名称: base.h
**       创建时间: 2025-08-03 (优化版本)
**       文件描述: 【HHBUI】引擎基础类库 - 现代化C++17线程和基础设施框架 （声明文件）
**
**       主要功能:
**       - 高性能现代化渲染线程管理
**       - 智能FPS计算与帧率控制系统
**       - 线程安全的高性能队列容器
**       - 基础UI对象抽象与生命周期管理
**       - 定时器信息管理与资源追踪
**
**       技术特性:
**       - 采用现代C++17标准与智能指针管理
**       - 异常安全保证与RAII资源管理
**       - 高性能原子操作与无锁编程
**       - 智能线程池与任务调度机制
**       - 实时性能监控与调试诊断
**
** =====================================================================================
**/

#pragma once
#include <thread>
#include <condition_variable>
#include <map>
#include <unordered_map>
#include <queue>
#include <memory>
#include <atomic>
#include <chrono>
#include <mutex>
#include <shared_mutex>
#include <optional>
#include <string_view>

namespace HHBUI
{
	/// 线程状态枚举
	enum class ThreadState : uint8_t
	{
		STOPPED = 0,
		RUNNING,
		PAUSED,
		STOPPING
	};

	/// 现代化渲染线程基类 - 支持RAII和异常安全
	class TOAPI UIRenderThread
	{
	public:
		UIRenderThread() noexcept;
		virtual ~UIRenderThread() noexcept;

		// 禁用拷贝构造和赋值
		UIRenderThread(const UIRenderThread&) = delete;
		UIRenderThread& operator=(const UIRenderThread&) = delete;

		// 支持移动语义
		UIRenderThread(UIRenderThread&& other) noexcept;
		UIRenderThread& operator=(UIRenderThread&& other) noexcept;

		/// 启动线程
		/// @param pause 是否以暂停状态启动
		/// @return 启动是否成功
		[[nodiscard]] bool Start(bool pause = false) noexcept;

		/// 暂停线程执行
		void Pause() noexcept;

		/// 恢复线程执行
		void Resume() noexcept;

		/// 停止线程并等待结束
		void Stop() noexcept;

		/// 获取当前线程状态
		[[nodiscard]] ThreadState GetState() const noexcept { return m_state.load(std::memory_order_acquire); }

		/// 检查线程是否正在运行
		[[nodiscard]] bool IsRunning() const noexcept { return GetState() == ThreadState::RUNNING; }

		/// 检查线程是否已暂停
		[[nodiscard]] bool IsPaused() const noexcept { return GetState() == ThreadState::PAUSED; }

		/// 获取线程ID（如果线程正在运行）
		[[nodiscard]] std::optional<std::thread::id> GetThreadId() const noexcept;

	protected:
		/// 纯虚函数：子类实现具体的渲染逻辑
		virtual void RenderThread() = 0;

		/// 虚函数：线程启动时的初始化回调
		virtual void OnThreadStart() {}

		/// 虚函数：线程停止时的清理回调
		virtual void OnThreadStop() {}

	private:
		void ThreadMain() noexcept;

		std::unique_ptr<std::thread> m_thread;
		mutable std::shared_mutex m_thread_mutex;
		std::condition_variable m_condition;
		std::mutex m_condition_mutex;
		std::atomic<ThreadState> m_state{ ThreadState::STOPPED };
	};
	/// FPS统计信息结构
	struct FPSStats
	{
		float current_fps = 0.0f;
		float average_fps = 0.0f;
		float min_fps = std::numeric_limits<float>::max();
		float max_fps = 0.0f;
		uint64_t total_frames = 0;
		std::chrono::milliseconds total_time{ 0 };
	};

	/// 现代化高精度FPS计数器 - 线程安全且高性能
	class TOAPI UIFPSCounter
	{
	public:
		UIFPSCounter() noexcept;
		~UIFPSCounter() = default;

		// 禁用拷贝，支持移动
		UIFPSCounter(const UIFPSCounter&) = delete;
		UIFPSCounter& operator=(const UIFPSCounter&) = delete;
		UIFPSCounter(UIFPSCounter&&) = default;
		UIFPSCounter& operator=(UIFPSCounter&&) = default;

		/// 计算并更新FPS
		/// @return 当前FPS值
		[[nodiscard]] float CalculateFPS() noexcept;

		/// 设置目标FPS限制
		/// @param target_fps 目标FPS，-1表示无限制
		void SetTargetFPS(float target_fps) noexcept;

		/// 获取目标FPS
		/// @return 目标FPS值，-1表示无限制
		[[nodiscard]] float GetTargetFPS() const noexcept;

		/// 执行FPS限制（阻塞到下一帧时间）
		void LimitFrameRate() noexcept;

		/// 重置所有统计数据
		void Reset() noexcept;

		/// 获取详细的FPS统计信息
		/// @return FPS统计结构
		[[nodiscard]] FPSStats GetStats() const noexcept;

		/// 获取当前FPS（非阻塞）
		/// @return 当前FPS值
		[[nodiscard]] float GetCurrentFPS() const noexcept;

		/// 获取平均FPS
		/// @return 平均FPS值
		[[nodiscard]] float GetAverageFPS() const noexcept;

		/// 标记帧开始
		void BeginFrame() noexcept;

		/// 标记帧结束
		void EndFrame() noexcept;

	private:
		void UpdateStats(float frame_time_ms) noexcept;

		mutable std::shared_mutex m_stats_mutex;

		// 时间相关
		std::chrono::high_resolution_clock::time_point m_last_time;
		std::chrono::high_resolution_clock::time_point m_frame_start_time;
		std::chrono::high_resolution_clock::time_point m_next_frame_time;

		// FPS统计
		std::atomic<float> m_current_fps{ 0.0f };
		std::atomic<float> m_target_fps{ -1.0f };
		std::atomic<uint32_t> m_frame_count{ 0 };

		// 详细统计（需要锁保护）
		FPSStats m_stats;
		std::chrono::nanoseconds m_frame_duration{ 0 };

		// 滑动窗口用于平滑FPS计算
		static constexpr size_t SAMPLE_WINDOW_SIZE = 60;
		std::array<float, SAMPLE_WINDOW_SIZE> m_frame_times{};
		size_t m_sample_index = 0;
		bool m_window_filled = false;
	};
	/// 高性能线程安全队列 - 支持现代C++17特性
	template <typename T>
	class TOAPI UIQueue
	{
	public:
		using value_type = T;
		using size_type = std::size_t;
		using reference = T&;
		using const_reference = const T&;

		UIQueue() = default;
		~UIQueue() = default;

		// 禁用拷贝，支持移动
		UIQueue(const UIQueue&) = delete;
		UIQueue& operator=(const UIQueue&) = delete;
		UIQueue(UIQueue&&) = default;
		UIQueue& operator=(UIQueue&&) = default;

		/// 检查队列是否为空
		/// @return true如果队列为空
		[[nodiscard]] bool empty() const noexcept
		{
			std::shared_lock<std::shared_mutex> lock(m_mutex);
			return m_queue.empty();
		}

		/// 获取队列大小
		/// @return 队列中元素数量
		[[nodiscard]] size_type size() const noexcept
		{
			std::shared_lock<std::shared_mutex> lock(m_mutex);
			return m_queue.size();
		}

		/// 在队列前端插入元素
		/// @param item 要插入的元素
		template<typename U = T>
		void push_front(U&& item)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_queue.emplace_front(std::forward<U>(item));
			m_condition.notify_one();
		}

		/// 在队列后端插入元素
		/// @param item 要插入的元素
		template<typename U = T>
		void push_back(U&& item)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_queue.emplace_back(std::forward<U>(item));
			m_condition.notify_one();
		}

		/// 从队列前端弹出元素
		/// @param item 输出参数，接收弹出的元素
		/// @return true如果成功弹出元素
		[[nodiscard]] bool try_pop_front(T& item) noexcept
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			if (m_queue.empty())
				return false;

			item = std::move(m_queue.front());
			m_queue.pop_front();
			return true;
		}

		/// 阻塞式从队列前端弹出元素
		/// @param item 输出参数，接收弹出的元素
		/// @return true如果成功弹出元素，false如果队列被关闭
		[[nodiscard]] bool wait_and_pop_front(T& item)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			while (m_queue.empty() && !m_shutdown)
			{
				m_condition.wait(lock);
			}

			if (m_shutdown)
				return false;

			item = std::move(m_queue.front());
			m_queue.pop_front();
			return true;
		}

		/// 带超时的弹出操作
		/// @param item 输出参数，接收弹出的元素
		/// @param timeout 超时时间
		/// @return true如果成功弹出元素
		template<typename Rep, typename Period>
		[[nodiscard]] bool wait_for_pop_front(T& item, const std::chrono::duration<Rep, Period>& timeout)
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			if (!m_condition.wait_for(lock, timeout, [this] { return !m_queue.empty() || m_shutdown; }))
				return false;

			if (m_shutdown)
				return false;

			item = std::move(m_queue.front());
			m_queue.pop_front();
			return true;
		}

		/// 清空队列
		void clear() noexcept
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_queue.clear();
		}

		/// 关闭队列，唤醒所有等待的线程
		void shutdown() noexcept
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);
			m_shutdown = true;
			m_condition.notify_all();
		}

		/// 检查队列是否已关闭
		/// @return true如果队列已关闭
		[[nodiscard]] bool is_shutdown() const noexcept
		{
			std::shared_lock<std::shared_mutex> lock(m_mutex);
			return m_shutdown;
		}

		// 兼容性方法（保持向后兼容）
		void insertquque(T& t) { push_front(t); }
		void enqueue(T& t) { push_back(t); }
		bool dequeue(T& t) { return try_pop_front(t); }

	private:
		mutable std::shared_mutex m_mutex;
		std::condition_variable_any m_condition;
		std::deque<T> m_queue;  // 使用deque替代list，更好的缓存局部性
		std::atomic<bool> m_shutdown{ false };
	};
	/// UI基础对象抽象类 - 现代化设计
	class TOAPI UIBase
	{
	public:
		UIBase() = default;
		virtual ~UIBase() = default;

		// 禁用拷贝，支持移动
		UIBase(const UIBase&) = delete;
		UIBase& operator=(const UIBase&) = delete;
		UIBase(UIBase&&) = default;
		UIBase& operator=(UIBase&&) = default;

		/// 获取UI视图指针（类型安全）
		template<typename T = void>
		[[nodiscard]] T* GetUIView() const noexcept
		{
			return static_cast<T*>(m_UIView);
		}

		/// 设置UI视图指针
		template<typename T>
		void SetUIView(T* view) noexcept
		{
			m_UIView = view;
		}

		/// 获取UI窗口指针（类型安全）
		template<typename T = void>
		[[nodiscard]] T* GetUIWindow() const noexcept
		{
			return static_cast<T*>(m_UIWindow);
		}

		/// 设置UI窗口指针
		template<typename T>
		void SetUIWindow(T* window) noexcept
		{
			m_UIWindow = window;
		}

		/// 检查是否有有效的UI视图
		[[nodiscard]] bool HasUIView() const noexcept { return m_UIView != nullptr; }

		/// 检查是否有有效的UI窗口
		[[nodiscard]] bool HasUIWindow() const noexcept { return m_UIWindow != nullptr; }

		/// 获取原始UI视图指针（兼容性方法）
		[[nodiscard]] void* GetRawUIView() const noexcept { return m_UIView; }

		/// 获取原始UI窗口指针（兼容性方法）
		[[nodiscard]] void* GetRawUIWindow() const noexcept { return m_UIWindow; }

		/// 设置原始UI视图指针（兼容性方法）
		void SetRawUIView(void* view) noexcept { m_UIView = view; }

		/// 设置原始UI窗口指针（兼容性方法）
		void SetRawUIWindow(void* window) noexcept { m_UIWindow = window; }

	protected:
		void* m_UIView = nullptr;
		void* m_UIWindow = nullptr;
		void* m_objChildFirst = nullptr;
		void* m_objChildLast = nullptr;

		friend class UIWnd;
		friend class UIControl;
		friend class UICanvas;
		friend class UILayout;
		friend class UIAnimation;
	};

	/// 定时器信息结构 - 兼容性版本
	struct TimerInfo
	{
		UIBase* pPropObj = nullptr;
		size_t nLocalID = 0;
		HWND hWnd = nullptr;
		UINT uWinTimer = 0;
		bool bKilled = false;
		std::chrono::steady_clock::time_point created_time = std::chrono::steady_clock::now();
		std::chrono::milliseconds interval{ 0 };

		/// 检查定时器是否仍然有效
		[[nodiscard]] bool IsValid() const noexcept
		{
			return !bKilled && pPropObj != nullptr;
		}

		/// 标记定时器为已销毁
		void Kill() noexcept
		{
			bKilled = true;
		}
	};

	/// 定时器信息容器类型
	using TimerInfoVector = std::vector<TimerInfo>;
	using VecTimerInfo = std::vector<TimerInfo>;

	/// 高性能定时器管理器
	class TOAPI UITimerManager
	{
	public:
		UITimerManager() = default;
		~UITimerManager() = default;

		// 禁用拷贝，支持移动
		UITimerManager(const UITimerManager&) = delete;
		UITimerManager& operator=(const UITimerManager&) = delete;
		UITimerManager(UITimerManager&&) = default;
		UITimerManager& operator=(UITimerManager&&) = default;

		/// 添加定时器
		/// @param timer_info 定时器信息
		/// @return 定时器ID
		[[nodiscard]] size_t AddTimer(TimerInfo timer_info);

		/// 移除定时器
		/// @param timer_id 定时器ID
		/// @return 是否成功移除
		bool RemoveTimer(size_t timer_id) noexcept;

		/// 获取定时器信息
		/// @param timer_id 定时器ID
		/// @return 定时器信息的可选值
		[[nodiscard]] std::optional<TimerInfo> GetTimer(size_t timer_id) const noexcept;

		/// 清理无效的定时器
		void CleanupInvalidTimers() noexcept;

		/// 获取活跃定时器数量
		[[nodiscard]] size_t GetActiveTimerCount() const noexcept;

	private:
		mutable std::shared_mutex m_mutex;
		std::unordered_map<size_t, TimerInfo> m_timers;
		std::atomic<size_t> m_next_timer_id{ 1 };
	};

} // namespace HHBUI