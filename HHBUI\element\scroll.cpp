﻿#include "pch.h"
#include "scroll.h"
#include <common/winapi.h>
//滚动条点击类型_调节按钮1
#define SBCT_ADJUSTBUTTON1 1
//滚动条点击类型_页面区域1
#define SBCT_PAGEAREA1 2
//滚动条点击类型_滚动条
#define SBCT_CONTROL 3
//滚动条点击类型_页面区域2
#define SBCT_PAGEAREA2 4
//滚动条点击类型_调节按钮2
#define SBCT_ADJUSTBUTTON2 5

HHBUI::UIScroll::UIScroll(UIBase *hParent, INT x, INT y, INT width, INT height, INT dwStyle, INT dwStyleEx, INT nID)
{
    InitSubControl(hParent, x, y, width, height, L"form-scroll", NULL, dwStyle, dwStyleEx, nID);
}

LRESULT HHBUI::UIScroll::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_CREATE)
	{
		m_data.dwFlags |= EOF_NOBORDER;
		BOOL bVS = ((m_data.dwStyle & ess_verticalscroll) == ess_verticalscroll);
		p_data.xyz = { UIEngine::ScaleValue(12),UIEngine::ScaleValue(12),UIEngine::ScaleValue(12),UIEngine::ScaleValue(30) };
		if (((m_data.dwStyle & eos_scroll_disableno) == eos_scroll_disableno))
		{
			p_data.wArrows = ESB_DISABLE_BOTH;
		}
        if (((m_data.dwStyle & eos_scroll_amds) == eos_scroll_amds))
        {
            sb_show(FALSE);//隐藏滚动条
        }
        p_data.color_normal = UIColor(139, 139, 139, 255);
        p_data.color_hover = UIColor(99, 99, 99, 255);
        p_data.color_down = UIColor(133, 133, 133, 255);
        p_data.color_btn_up = p_data.color_normal;
        p_data.color_btn_down = p_data.color_normal;
        p_data.hBrush = new UIBrush();
	}
    else if (uMsg == WM_MOUSEMOVE)
    {
        INT x = GET_X_LPARAM(lParam);
        INT y = GET_Y_LPARAM(lParam);
        if (((m_data.dwStyle & ess_verticalscroll) == ess_verticalscroll))
        {
            if (y >= p_data.nMin && y <= p_data.nMax + p_data.rcRegion.bottom - p_data.rcRegion.top)
            {
                sb_mousemove(wParam, x, y);
            }
        }
        else if (((m_data.dwStyle & ess_horizontalscroll) == ess_horizontalscroll))
        {
            if (x >= p_data.nMin && x <= p_data.nMax + p_data.rcRegion.right - p_data.rcRegion.left)
            {
                sb_mousemove(wParam, x, y);
            }
        }
    }
    else if (uMsg == WM_NCHITTEST)
    {
        sb_nchittest(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
    }
    else if (uMsg == WM_SIZE)
    {
        sb_nccalcsize();
    }
    else if (uMsg == WM_CONTEXTMENU)
    {
        sb_oncontextmenu(lParam);
    }
    else if (uMsg == WM_MOUSEHOVER)
    {
        if (((m_data.dwStyle & eos_scroll_amds) == eos_scroll_amds))
        {
            sb_show(TRUE); //显示滚动条
        }
        SetState(state_hover, FALSE);
        Redraw();
    }
    else if (uMsg == WM_MOUSELEAVE)
    {
        if (((m_data.dwStyle & eos_scroll_amds) == eos_scroll_amds))
        {
            sb_show(FALSE);//隐藏滚动条
        }
        SetState(state_hover, TRUE);
        Redraw();
    }
    else if (uMsg == WM_LBUTTONDOWN)
    {
        sb_leftbuttondown(lParam);
    }
    else if (uMsg == WM_LBUTTONUP)
    {
        SetState(state_down, TRUE);
        p_data.nTrackPos = 0;
        ::KillTimer(hWnd, (size_t)this + TIMER_SCROLLBAR);
        Redraw();
    }
    else if (uMsg == WM_MOUSEWHEEL || uMsg == WM_HSCROLL || uMsg == WM_VSCROLL)
    {
        sb_parentnotify(wParam, lParam, uMsg);
    }
    else if (uMsg == SBM_SETSCROLLINFO)
    {
        return sb_realsetinfo(hWnd, __get_int((LPVOID)lParam, 4), __get_int((LPVOID)lParam, 8), __get_int((LPVOID)lParam, 12), __get_int((LPVOID)lParam, 16), __get_int((LPVOID)lParam, 20), wParam != 0);
    }
    else if (uMsg == SBM_SETPOS)
    {
        return sb_realsetinfo(hWnd, SIF_POS, 0, 0, 0, wParam, lParam != 0);
    }
    else if (uMsg == SBM_GETPOS)
    {
        return p_data.nPos;
    }
    else if (uMsg == SBM_SETRANGE)
    {
        return sb_realsetinfo(hWnd, SIF_RANGE, wParam, lParam, 0, 0, FALSE);
    }
    else if (uMsg == SBM_GETRANGE)
    {
    }
    else if (uMsg == WM_SETFOCUS)
    {
        return 0;
    }
    else if (uMsg == WM_COMMAND)
    {
        sb_oncommand(wParam, lParam);
    }
    else if (uMsg == WM_DESTROY)
    {
        delete p_data.hBrush;
    }
	return S_OK;
}

void HHBUI::UIScroll::sb_timer(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime)
{
    UIScroll* pObj = (UIScroll*)(idEvent - TIMER_SCROLLBAR);
    INT nTrack;
    if (pObj->p_data.httype == SBCT_ADJUSTBUTTON1)
    {
        nTrack = SB_LINEUP;
    }
    else
    {
        nTrack = SB_LINEDOWN;
    }
    pObj->sb_parentnotify(MAKELONG(nTrack, 0), 0, 0);
    nTrack = pObj->p_data.nPos;
    if (pObj->p_data.httype == SBCT_ADJUSTBUTTON1)
    {
        if (nTrack <= pObj->p_data.nMin)
        {
            ::KillTimer(hWnd, idEvent);
        }
    }
    else
    {
        if (nTrack >= pObj->p_data.nMax - pObj->p_data.nPage)
        {
            ::KillTimer(hWnd, idEvent);
        }
    }
}

INT HHBUI::UIScroll::sb_OnScrollbar(INT uMsg, WPARAM wParam, LPARAM lParam, INT nPage, INT nLine)
{
    BOOL bHScoll = uMsg == WM_HSCROLL;
    INT64 nMin = p_data.nMin;
    INT64 nMax = p_data.nMax;
    INT64 oPos = p_data.nPos;
    INT64 nPos = 0;
    if (nLine < 1)
        nLine = 40;
    INT nCode = LOWORD(wParam);
    if (nMax <= 0)
        return 0;
    if (nCode == SB_PAGEUP)
    {
        nPos = oPos - nPage;
    }
    else if (nCode == SB_PAGEDOWN)
    {
        nPos = oPos + nPage;
    }
    else if (nCode == SB_LINEUP)
    {
        nPos = oPos - nLine;
    }
    else if (nCode == SB_LINEDOWN)
    {
        nPos = oPos + nLine;
    }
    else if (nCode == SB_TOP)
    {
        nPos = nMin;
    }
    else if (nCode == SB_BOTTOM)
    {
        nPos = nMax;
    }
    else
    {
        oPos = nMin;
        nPos = p_data.nTrackPos;

    }
    if (nPos < nMin)
    {
        nPos = nMin;
    }
    if (nPos > nMax)
    {
        nPos = nMax;
    }
    if (nPos != oPos)
        sb_realsetinfo(GethWnd(), SIF_POS | SIF_PAGE, nMin, nMax, nPage, nPos, TRUE);
    return nPos;
}

void HHBUI::UIScroll::OnPaintProc(ps_context ps)
{
    INT httype = p_data.httype;
    BOOL bHover = (ps.dwState & state_hover) != 0;
    BOOL bDown = (ps.dwState & state_down) != 0;
    BOOL bVScroll = (ps.dwStyle & ess_verticalscroll) != 0;
    BOOL bshow = (ps.dwStyle & eos_scroll_disableno) != 0;
    if (!bshow)
    {
        UIColor crColor = p_data.color_normal;
        p_data.hBrush->SetColor(crColor);
        if (((m_data.dwStyle & ess_controlbutton) == ess_controlbutton))
        {
            if (bHover)
            {
                crColor = p_data.color_btn_up;
                if (httype == SBCT_ADJUSTBUTTON1)
                {
                    if (bDown)
                    {
                        crColor.SetColorLights(1.0f);
                    }
                    else
                    {
                        crColor.SetColorLights(0.6f);
                    }
                }
                p_data.hBrush->SetColor(crColor);
                auto rcSrc = p_data.rcArrow1;

                if (bVScroll)
                    ps.hCanvas->FillPoly(p_data.hBrush, rcSrc.left, rcSrc.top + 2, rcSrc.right - 3, rcSrc.bottom - 2, 3, 270.f);
                else
                    ps.hCanvas->FillPoly(p_data.hBrush, rcSrc.left, rcSrc.top + 1, rcSrc.right - 2, rcSrc.bottom - 1, 3, 180.f);

            }

            if (bHover)
            {
                crColor = p_data.color_btn_down;
                if (httype == SBCT_ADJUSTBUTTON2)
                {
                    if (bDown)
                    {
                        crColor.SetColorLights(1.0f);
                    }
                    else
                    {
                        crColor.SetColorLights(0.6f);
                    }
                }
                p_data.hBrush->SetColor(crColor);
                auto rcSrc = p_data.rcArrow2;
                if (bVScroll)
                    ps.hCanvas->FillPoly(p_data.hBrush, rcSrc.left, rcSrc.top + 2, rcSrc.right - 2, rcSrc.bottom - 2, 3, 90.f);
                else
                    ps.hCanvas->FillPoly(p_data.hBrush, rcSrc.left, rcSrc.top + 1, rcSrc.right - 2, rcSrc.bottom - 1, 3);
                //RECT rcRegion = p_data.rcRegion;
                //alpha = ((p_data.wArrows & ESB_DISABLE_BOTH) == ESB_DISABLE_BOTH) ? 128 : 255;

            }
        }
        auto rcThumb = p_data.rcThumb;
        if (!rcThumb.empty())
        {
            crColor = p_data.color_normal;
            if (httype == SBCT_CONTROL)
            {
                if (bDown)
                {
                    crColor = p_data.color_down;
                    crColor.SetColorLights(0.8f);
                }
                else
                {
                    if (bHover)
                    {
                        crColor = p_data.color_hover;
                        crColor.SetColorLights(0.9f);
                    }
                }
            }
            p_data.hBrush->SetColor(crColor);
            if (bHover || bDown)
            {
                if (bVScroll)
                {
                    ps.hCanvas->FillRoundRect(p_data.hBrush, rcThumb.left, rcThumb.top, rcThumb.right - 3, rcThumb.bottom, p_data.isRadius ? 4 : 0);
                }
                else
                {
                    ps.hCanvas->FillRoundRect(p_data.hBrush, rcThumb.left - 2, rcThumb.top, rcThumb.right, rcThumb.bottom - 1, p_data.isRadius ? 4 : 0);
                }
            }
            else
            {
                if (bVScroll)
                {
                    ps.hCanvas->FillRoundRect(p_data.hBrush, rcThumb.right - 9, rcThumb.top, rcThumb.right - 3, rcThumb.bottom, p_data.isRadius ? 3 : 0);
                }
                else
                {
                    ps.hCanvas->FillRoundRect(p_data.hBrush, rcThumb.left - 2, rcThumb.bottom - 9, rcThumb.right, rcThumb.bottom - 3, p_data.isRadius ? 3 : 0);
                }

            }
        }
    }
}

void HHBUI::UIScroll::sb_nccalcsize()
{
    BOOL bVScroll = ((m_data.dwStyle & ess_verticalscroll) == ess_verticalscroll);
    INT cx = 0, cy = 0;
    if (((m_data.dwStyle & ess_controlbutton) == ess_controlbutton))
    {
        cx = p_data.xyz.left;
        cy = p_data.xyz.top;
    }
    auto rcClient = m_data.Frame_c;
    if (bVScroll)
    {
        p_data.rcArrow1.left = rcClient.left;
        p_data.rcArrow1.top = rcClient.top;
        p_data.rcArrow1.right = rcClient.right;
        p_data.rcArrow1.bottom = rcClient.top + cx;

        p_data.rcArrow2.left = rcClient.left;
        p_data.rcArrow2.top = rcClient.bottom - cy;
        p_data.rcArrow2.right = rcClient.right;
        p_data.rcArrow2.bottom = rcClient.bottom;

        p_data.rcRegion.left = rcClient.left;
        p_data.rcRegion.top = rcClient.top + cx;
        p_data.rcRegion.right = rcClient.right;
        p_data.rcRegion.bottom = rcClient.bottom - cy;
    }
    else
    {
        p_data.rcArrow1.left = rcClient.left;
        p_data.rcArrow1.top = rcClient.top;
        p_data.rcArrow1.right = rcClient.left + cx;
        p_data.rcArrow1.bottom = rcClient.bottom;

        p_data.rcArrow2.left = rcClient.right - cy;
        p_data.rcArrow2.top = rcClient.top;
        p_data.rcArrow2.right = rcClient.right;
        p_data.rcArrow2.bottom = rcClient.bottom;

        p_data.rcRegion.left = rcClient.left + cx;
        p_data.rcRegion.top = rcClient.top;
        p_data.rcRegion.right = rcClient.right - cy;
        p_data.rcRegion.bottom = rcClient.bottom;
    }
    sb_calcthumb(bVScroll);
}

void HHBUI::UIScroll::sb_calcthumb(BOOL bVScroll)
{
    INT cxy = 0;
    INT point = sb_pos2point(bVScroll, cxy);
    if (bVScroll)
    {
        p_data.rcThumb.left = p_data.rcRegion.left;
        p_data.rcThumb.top = point;
        p_data.rcThumb.right = p_data.rcRegion.right;
        p_data.rcThumb.bottom = point + cxy;
    }
    else
    {
        p_data.rcThumb.left = point;
        p_data.rcThumb.top = p_data.rcRegion.top;
        p_data.rcThumb.right = point + cxy;
        p_data.rcThumb.bottom = p_data.rcRegion.bottom;
    }
}

INT HHBUI::UIScroll::sb_pos2point(BOOL bVert, INT& cxy)
{
    INT64 nPage = p_data.nPage;
    INT64 nMin = p_data.nMin;
    INT64 nMax = p_data.nMax;
    INT64 nMinThumbsize2 = p_data.xyz.bottom;
    INT64 maxPos = nMax - nMin;
    INT64 sizeRegin = bVert ? p_data.rcRegion.bottom - p_data.rcRegion.top : p_data.rcRegion.right - p_data.rcRegion.left;
    INT64 point = 0;
    if (maxPos > 0)
    {
        if (p_data.nPos < nMin)
        {
            p_data.nPos = nMin;
        }
        else
        {
            if (nMax < p_data.nPos)
            {
                p_data.nPos = nMax;
            }
        }
        cxy = (int)(sizeRegin * nPage / (maxPos + nPage));
        //*cxy = sizeRegin * nPage / (maxPos + nPage);
        if (cxy < nMinThumbsize2)
        {
            cxy = (int)nMinThumbsize2;
        }
        point = (p_data.nPos - nMin) * (sizeRegin - cxy) / maxPos;
        //output(point);
    }
    point = point + (bVert ? p_data.rcRegion.top : p_data.rcRegion.left);
    return (int)point;
}

INT HHBUI::UIScroll::sb_point2pos(INT x, INT y, BOOL bVert, BOOL bCheckPos)
{
    INT64 nMin = p_data.nMin;
    INT64 nMax = p_data.nMax;
    INT64 maxpos = nMax - nMin;
    INT64 sizeRegion = bVert ? (static_cast<INT64>(p_data.rcRegion.bottom) - p_data.rcRegion.top) : (static_cast<INT64>(p_data.rcRegion.right) - p_data.rcRegion.left);
    INT64 curPoint = (static_cast<INT64>(bVert ? (y - p_data.rcRegion.top) : (x - p_data.rcRegion.left))) - p_data.nTrackPosOffset;
    INT64 sizeThumb = bVert ? (static_cast<INT64>(p_data.rcThumb.bottom) - p_data.rcThumb.top) : (static_cast<INT64>(p_data.rcThumb.right) - p_data.rcThumb.left);

    INT nPos = curPoint * maxpos / (sizeRegion - sizeThumb) + nMin;
    if (bCheckPos)
    {
        nPos = (nPos >= nMax) ? nMax : nPos;
        nPos = (nPos <= nMin) ? nMin : nPos;
    }

    return nPos;
}

INT HHBUI::UIScroll::sb_realsetinfo(HWND hWnd, INT Mask, INT nMin, INT nMax, INT nPage, INT nPos, BOOL bRedraw)
{
    if ((Mask & SIF_POS) != 0)
    {
        p_data.nPos = nPos;
    }
    if ((Mask & SIF_PAGE) != 0)
    {
        p_data.nPage = nPage;
    }
    if ((Mask & SIF_RANGE) != 0)
    {
        p_data.nMin = nMin;
        p_data.nMax = (nMax > 0 ? nMax : 0);
    }
    INT nPosOrg = p_data.nPos;
    sb_calcthumb((m_data.dwStyle & ess_verticalscroll) == ess_verticalscroll);
    nPos = p_data.nPos;
    if (nPos != nPosOrg) {
        sb_parentnotify(MAKELONG(SB_THUMBPOSITION, nPos), 0, 0);
    }
    if (bRedraw)
        Redraw();
    return nPos;
}

void HHBUI::UIScroll::sb_nchittest(INT x, INT y)
{
    INT httype = NULL;
    if (p_data.rcArrow1.PtInRect(x,y))
    {
        if (!((p_data.wArrows & ESB_DISABLE_LEFT) == ESB_DISABLE_LEFT))
        {
            httype = SBCT_ADJUSTBUTTON1;
        }
    }
    else
    {
        if (p_data.rcArrow2.PtInRect(x,y))
        {
            if (!((p_data.wArrows & ESB_DISABLE_RIGHT) == ESB_DISABLE_RIGHT))
            {
                httype = SBCT_ADJUSTBUTTON2;
            }
        }
        else
        {
            if (p_data.wArrows != ESB_DISABLE_BOTH)
            {
                if (p_data.rcThumb.PtInRect(x,y))
                {
                    httype = SBCT_CONTROL;
                }
                else
                {
                    if (p_data.rcRegion.PtInRect(x,y))
                    {
                        if (((m_data.dwStyle & ess_verticalscroll) == ess_verticalscroll))
                        {
                            if (y <= (int)p_data.rcThumb.top)
                            {
                                httype = SBCT_PAGEAREA1;
                            }
                            else
                            {
                                httype = SBCT_PAGEAREA2;
                            }
                        }
                        else
                        {
                            if (x <= p_data.rcThumb.left)
                            {
                                httype = SBCT_PAGEAREA1;
                            }
                            else
                            {
                                httype = SBCT_PAGEAREA2;
                            }
                        }
                    }
                }
            }
        }
    }
    INT oldhttype = p_data.httype;
    p_data.httype = httype;
    if (oldhttype != httype)
        Redraw();
}

void HHBUI::UIScroll::sb_mousemove(WPARAM wParam, INT x, INT y)
{
    if (wParam != 0)
    {
        if (p_data.httype == SBCT_CONTROL)
        {
            INT lstPos = p_data.nTrackPos;
            INT curPos = sb_point2pos(x, y, ((m_data.dwStyle & ess_verticalscroll) == ess_verticalscroll), TRUE);
            if (lstPos != curPos)
            {
                p_data.nTrackPos = curPos;
                if (sb_parentnotify(MAKELONG(SB_THUMBTRACK, curPos), 0, 0) == 0)
                {
                    p_data.nPos = curPos;
                    sb_parentnotify(MAKELONG(SB_THUMBPOSITION, curPos), 0, 0);
                }
            }
        }
    }
}
size_t HHBUI::UIScroll::sb_parentnotify(WPARAM wParam, LPARAM lParam, INT uMsg)
{
    // 如果 uMsg 为 0，则根据 m_data.dwStyle 的垂直滚动条位来确定 uMsg
    if (uMsg == 0)
    {
        uMsg = (m_data.dwStyle & ess_verticalscroll) ? WM_VSCROLL : WM_HSCROLL;
    }

    auto pParentBase = GetParent();
    if (!pParentBase)
    {
        return 0;
    }
    auto pUIView = (UIControl*)pParentBase->GetRawUIView();
    if (!pUIView)
    {
        return 0;
    }
    return pUIView->OnBaseProc(m_data.pWnd->GethWnd(), uMsg, wParam, lParam);
}

void HHBUI::UIScroll::sb_leftbuttondown(LPARAM lParam)
{
    INT httype = p_data.httype;
    SetState(state_down, FALSE);
    Redraw();
    if (httype != 0)
    {
        INT nTrack = -1;
        INT x, y;
        BOOL fTimer = FALSE;
        if (httype == SBCT_CONTROL)
        {
            x = LOWORD(lParam) - p_data.rcThumb.left;
            y = HIWORD(lParam) - p_data.rcThumb.top;
            p_data.nTrackPosOffset = ((m_data.dwStyle & ess_verticalscroll) == ess_verticalscroll) ? y : x;
            p_data.nTrackPos = p_data.nPos;
        }
        else if (httype == SBCT_ADJUSTBUTTON1)
        {
            nTrack = SB_LINEUP;
            fTimer = TRUE;
        }
        else if (httype == SBCT_ADJUSTBUTTON2)
        {
            nTrack = SB_LINEDOWN;
            fTimer = TRUE;
        }
        else if (httype == SBCT_PAGEAREA1)
        {
            nTrack = SB_PAGEUP;
        }
        else if (httype == SBCT_PAGEAREA2)
        {
            nTrack = SB_PAGEDOWN;
        }

        if (nTrack != -1)
        {
            sb_parentnotify(MAKELONG(nTrack, 0), 0, 0);
        }
        if (fTimer)
        {
            ::SetTimer(m_data.pWnd->GethWnd(), (size_t)this + TIMER_SCROLLBAR, 200, sb_timer);
        }
    }
}

void HHBUI::UIScroll::sb_oncommand(WPARAM wParam, LPARAM lParam)
{
    BOOL fNotify = TRUE;
    INT nPos = NULL;
    INT nCode;
    if (wParam == 4100)
    {
        INT nTrackPosOffset = p_data.nTrackPosOffset;
        p_data.nTrackPosOffset = 0;
        ExRectF lptRect{};
        m_data.pWnd->GetRect(lptRect);
        nPos = sb_point2pos(LOWORD(nTrackPosOffset) - m_data.Frame_w.left - lptRect.left, HIWORD(nTrackPosOffset) - m_data.Frame_w.top - lptRect.top, ((m_data.dwStyle & ess_verticalscroll) == ess_verticalscroll), TRUE);
        p_data.nTrackPos = nPos;
        nCode = SB_THUMBPOSITION;
    }
    else if (wParam == 4102)
    {
        nCode = SB_TOP;
    }
    else if (wParam == 4103)
    {
        nCode = SB_BOTTOM;
    }
    else if (wParam == 4096)
    {
        nCode = SB_LINEUP;
    }
    else if (wParam == 4097)
    {
        nCode = SB_LINEDOWN;
    }
    else if (wParam == 4098)
    {
        nCode = SB_PAGEUP;
    }
    else if (wParam == 4099)
    {
        nCode = SB_PAGEDOWN;
    }
    else
    {
        fNotify = FALSE;
    }
    if (fNotify)
    {
        sb_parentnotify(MAKELONG(nCode, nPos), 0, 0);
    }
}

void HHBUI::UIScroll::sb_oncontextmenu(LPARAM lParam)
{
    HMENU hMenu = ((m_data.dwStyle & ess_verticalscroll) == ess_verticalscroll) ? UIWinApi::ToList.hMenuVS : UIWinApi::ToList.hMenuHS;
    hMenu = GetSubMenu(hMenu, 0);
    p_data.nTrackPosOffset = lParam;
    INT wEnable = ((p_data.wArrows & ESB_DISABLE_BOTH) == ESB_DISABLE_BOTH) ? 1026 : 1024;
    EnableMenuItem(hMenu, 0, wEnable); //滚动至此
    wEnable = (((p_data.wArrows & ESB_DISABLE_LEFT) == ESB_DISABLE_LEFT) || (p_data.nPos == p_data.nMin)) ? 1026 : 1024;
    EnableMenuItem(hMenu, 2, wEnable); //顶部
    EnableMenuItem(hMenu, 5, wEnable); //上页
    EnableMenuItem(hMenu, 8, wEnable); //上行
    wEnable = (((p_data.wArrows & ESB_DISABLE_RIGHT) == ESB_DISABLE_RIGHT) || (p_data.nPos == p_data.nMax)) ? 1026 : 1024;
    EnableMenuItem(hMenu, 3, wEnable); //底部
    EnableMenuItem(hMenu, 6, wEnable); //下页
    EnableMenuItem(hMenu, 9, wEnable); //下行
    
    UIMenu::Popup(m_data.pWnd, hMenu, LOWORD(lParam), HIWORD(lParam));
}

void HHBUI::UIScroll::sb_show(BOOL fshow)
{
    m_data.alpha = fshow ? 255 : 0;
    Redraw();
}

void HHBUI::UIScroll::sb_set_wArrows(INT wArrows, BOOL fRedraw)
{
    p_data.wArrows = wArrows;
    if (wArrows == ESB_DISABLE_BOTH)
    {
        p_data.rcThumb = {0,0,0,0};
    }
    if (fRedraw)
    {
        Redraw();
    }
}

