﻿#include "pch.h"
#include "table.h"
#include <iostream>
#include <sstream>
#include <algorithm> // 添加algorithm头文件，提供min函数
#include <common/winapi.h>
#include <common/data.h>
#include <common/memory.h>
#include "page.h"

struct reportlistview_tr_s
{
    UINT nInsertIndex;
    DWORD dwStyle;     //项目行风格
    LPVOID pTDInfo; //行文本数组信息
};
struct info_table_head
{
    LPCWSTR pwzText;    //表头标题
    UINT nWidth;       //列宽度
    DWORD dwStyle;      //表头风格
    DWORD dwTextFormat; //列文本格式
    HHBUI::UIColor crText;      //列文本颜色
    HHBUI::UIColor crbk;        //列背景颜色
    HHBUI::UIColor crbk_hover;     //列背景点燃颜色
    HHBUI::UIImage* nImage;     //图标
    UINT nInsertIndex;  //插入位置,0为在最后
    FLOAT ntextWidth;   //列文本宽度
    BOOL fDesc;        //是否倒序
    UINT imgWidth;
    UINT imgHeight;
};
struct info_table_head_s
{
    UINT nInsertIndex;
    DWORD dwStyle;     //项目行风格
    LPVOID pTDInfo; //行文本数组信息
};
struct reportlistview_td_s
{
    LPCWSTR wzText;
    HHBUI::UIColor crText;      //项目文本颜色
    HHBUI::UIColor crbk;        //项目背景颜色
    LPARAM lParam;    //项目附加参数
    HHBUI::UIImage* nImageIndex; //项目图片索引
    DWORD dwStyle;     //项目风格
    BOOL dwMerge;      //合并
    BOOL dwMergebottom;//是否到合并底部
    BOOL dwMergeright;//是否到合并右边
    UINT imgWidth;
    UINT imgHeight;
    INT iRow;         //起始合并行
    INT iCol;         //起始合并列
    INT iendRow;      //终止合并行
    INT iendCol;      //终止合并列
};

HHBUI::UITable_Head::UITable_Head(UIBase *hParent, INT x, INT y, INT width, INT height, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
    InitSubControl(hParent, x, y, width, height, L"form-table-head", 0, dwStyle, dwStyleEx, nID, dwTextFormat);
    m_data.dwFlags |= EOF_NOBORDER;
    s_data.hBrush = new UIBrush();
}

void HHBUI::UITable_Head::Check(size_t iCol, BOOL fCheck)
{
    auto data_s = (UITable*)m_data.lParam;
    info_table_head* pTC = (info_table_head*)((size_t)data_s->s_data.tcinfo + (iCol - 1) * sizeof(info_table_head));
    BOOL fcheckbox = (pTC->dwStyle & cs_table_checkbox) == cs_table_checkbox;
    if (fCheck)
    {
        if (fcheckbox)
            FLAGS_ADD(pTC->dwStyle, rs_table_checkboxok);
        for (size_t i = 0; i < data_s->s_data.itemList->size(); i++)
        {
            auto ptr = (info_table_head_s*)data_s->s_data.itemList->at(i);
            FLAGS_ADD(ptr->dwStyle, rs_table_checkboxok);
        }
    }
    else
    {
        if (fcheckbox)
            FLAGS_DEL(pTC->dwStyle, rs_table_checkboxok);
        for (size_t i = 0; i < data_s->s_data.itemList->size(); i++)
        {
            auto ptr = (info_table_head_s*)data_s->s_data.itemList->at(i);
            FLAGS_DEL(ptr->dwStyle, rs_table_checkboxok);
        }
    }
    Redraw();
}

void HHBUI::UITable_Head::Update()
{
    auto data_s = (UITable*)m_data.lParam;
    LPVOID pTCs = data_s->s_data.tcinfo;
    // 计算总宽度
    INT nWidth = 0;
    for (INT i = 0; i < data_s->s_data.ctcs; i++)
    {
        nWidth += ((info_table_head*)((size_t)pTCs + i * sizeof(info_table_head)))->nWidth;
    }
    //nWidth = UIEngine::fScale(nWidth);
    // 更新 itemwidth
    data_s->s_data.itemwidth = nWidth;
   
    ExRectF rc{};
    data_s->GetRect(rc, 0, TRUE);
    INT Width = rc.right - rc.left;
    if (nWidth > Width)
        Width = nWidth;
    data_s->p_data.width_item = Width / UIWinApi::ToList.drawing_default_dpi;
    // 调整视图大小
    data_s->SendMsg(WM_SIZE, 0, MAKELONG(rc.right - rc.left, rc.bottom - rc.top));
    // 检查是否需要滚动
    if (nWidth < (rc.right - rc.left) - 1)
    {
        data_s->SendMsg(WM_HSCROLL, MAKELONG(4, 0), 0); // 列宽小于组件宽度，让横向滚动条滚动到最左边。
    }
    Redraw();
}

LRESULT HHBUI::UITable_Head::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
    if (uMsg == WM_MOUSELEAVE) //当离开表头
    {
        if ((m_data.dwState & state_allowdrag) == 0) //并且未拖动时
        {
            auto data_s = (UITable*)m_data.lParam;
            data_s->s_data.indexhit = 0;
            Redraw();
        }
    }
    else if (uMsg == WM_LBUTTONDOWN) //当单击表头
    {
        INT nHitBlock = 0;
        auto data_s = (UITable*)m_data.lParam;
        INT nIndex = (INT)hittest(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam), FALSE, &nHitBlock);
        if (nIndex != 0 && nHitBlock == 0 && data_s->s_data.itemList->size()) //若命中某一列
        {
            auto pTC = (info_table_head*)((size_t)data_s->s_data.tcinfo + (nIndex - 1) * sizeof(info_table_head));
            if ((pTC->dwStyle & cs_table_checkbox) == cs_table_checkbox)
            {
                //FLAGS_DEL(pTC->dwStyle, rs_table_halfselect);
                BOOL f = (pTC->dwStyle & rs_table_checkboxok) != rs_table_checkboxok;
                Check(nIndex, f);

            }
            else if ((pTC->dwStyle & cs_table_clickable) == cs_table_clickable) //如果它是可点击的
            {
                if (data_s->DispatchNotify(WMM_RLVN_COLUMNCLICK, nIndex, lParam) == S_OK) //分发事件
                {
                    if ((pTC->dwStyle & cs_table_sortable_ab) != 0 || (pTC->dwStyle & cs_table_sortable_number) != 0)
                    {
                        data_s->Sort(nIndex, pTC->fDesc);
                    }
                }
               
            }
        }
        else
        {
            if (nHitBlock == 2) //如果命中位置是分割线,则开始拖动
            {
                SetState(state_allowdrag, FALSE);
            }
        }
        SetState(state_down, FALSE);
        Redraw();
    }
    else if (uMsg == WM_LBUTTONUP)
    {
        auto data_s = (UITable*)m_data.lParam;
        SetState(state_down | state_allowdrag, TRUE);
        Redraw();
        INT rHitBlock = 0;
        hittest(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam), FALSE, &rHitBlock);
        Update();
        data_s->UpdateIndex(data_s->p_data.index_start, data_s->p_data.index_end);
    }
    else if (uMsg == WM_MOUSEMOVE)
    {
        INT rHitBlock = 0;
        auto data_s = (UITable*)m_data.lParam;
        INT nIndex = (INT)hittest(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam), TRUE, &rHitBlock);
        if (nIndex != 0)
        {
            if (rHitBlock == 2 || (m_data.dwState & state_allowdrag) != 0) //如果是正在拖动,则更新拖动列的宽度,并更新列表
            {
                SetCursor(IDC_SIZEWE);
                if ((m_data.dwState & state_down) != 0)
                {
                    INT w = -data_s->GetScrollPos(TRUE);
                    LPVOID pTCs = data_s->s_data.tcinfo;
                    for (INT i = 1; i <= nIndex; i++)
                    {
                        auto p = (info_table_head*)__ptr_index(pTCs, nIndex, i, sizeof(info_table_head));
                        if (nIndex == i)
                        {
                            INT x = GET_X_LPARAM(lParam);
                            ExRectF rc;
                            data_s->GetRect(rc, 0, TRUE);
                            if (x > 1 && x < rc.right - 1)
                            {
                                w = x - w;
                                if (w < UIEngine::ScaleValue(35))
                                {
                                    w = UIEngine::ScaleValue(35);
                                }
                                p->nWidth = w;
                                data_s->Redraw();
                                Redraw();
                                break;
                            }
                        }
                        w = w + p->nWidth;
                    }
                    return 0;
                }
            }
            else
            {
                SetCursor(IDC_ARROW);
                if (data_s->s_data.indexhit != nIndex)
                {
                    LPVOID pTCs = data_s->s_data.tcinfo;
                    auto p = (info_table_head*)__ptr_index(pTCs, nIndex, nIndex, sizeof(info_table_head));
                    if (p->nWidth <= p->ntextWidth)
                        TooltipsPop(p->pwzText, NULL, 0, -1, -1, -1, FALSE);
                    data_s->s_data.indexhit = nIndex;
                    Redraw();
                }
            }
        }
        else
        {
            SetCursor(IDC_ARROW);
            if (data_s->s_data.indexhit != nIndex)
            {
                data_s->s_data.indexhit = nIndex;
                Redraw();
            }
        }
    }
    else if (uMsg == WM_DESTROY)
    {
        delete s_data.hBrush;
    }
    return S_OK;
}

void HHBUI::UITable_Head::OnPaintProc(ps_context ps)
{
    auto data_s = (UITable*)m_data.lParam;
    LPVOID pTCs = data_s->s_data.tcinfo;
    FLOAT strokeWidth = (FLOAT)data_s->s_data.linewidth;
    BOOL bShowAllwasy = ((data_s->m_data.dwStyle & eos_table_showtable) == eos_table_showtable);
    BOOL ballowmultiple = ((data_s->m_data.dwStyle & eos_table_allowmultiple) != 0);
    if (pTCs != 0 && data_s->s_data.ctcs > 0)
    {
        INT nOffsetX = -data_s->GetScrollPos(TRUE);
        ExRectF checkrc{ 0.f,0.f,UIEngine::ScaleValue(16),UIEngine::ScaleValue(16) };
        for (INT i = 1; i <= data_s->s_data.ctcs; i++)
        {
            auto ptr = (info_table_head*)((size_t)pTCs + (i - 1) * sizeof(info_table_head));
            INT nColWidth = ptr->nWidth;
            if (nColWidth > 0)
            {
                s_data.hBrush->SetColor(ptr->crbk);
                if (data_s->s_data.indexhit == i && (ptr->dwStyle & cs_table_clickable) == cs_table_clickable)
                {
                    s_data.hBrush->SetColor(ptr->crbk_hover);
                }
                ps.hCanvas->FillRect(s_data.hBrush, nOffsetX, 0, nOffsetX + nColWidth + strokeWidth, ps.uHeight);

                BOOL fsort = (ptr->dwStyle & cs_table_sortable_ab) != 0 || (ptr->dwStyle & cs_table_sortable_number) != 0;
                if ((ptr->dwStyle & cs_table_checkbox) != 0 && !bShowAllwasy && ballowmultiple)
                {
                    RECT tpchec
                    { (LONG)(nOffsetX + (nOffsetX + nColWidth - 3 - checkrc.right) / 2),
                    (LONG)((ps.uHeight - checkrc.bottom) / 2),
                    (LONG)(nOffsetX + (nOffsetX + nColWidth - 3 - checkrc.right) / 2 + checkrc.right),
                    (LONG)((ps.uHeight - checkrc.bottom) / 2 + checkrc.bottom)
                    };

                    s_data.hBrush->SetColor(data_s->s_data.select);
                    auto ret = ExRectF(tpchec.left, tpchec.top, tpchec.right - tpchec.left, tpchec.bottom - tpchec.top, TRUE);
                    if ((ptr->dwStyle & rs_table_checkboxok) != 0)
                    {
                        ps.hCanvas->FillRoundRect(s_data.hBrush, ret.left, ret.top, ret.right, ret.bottom, 2);
                        s_data.hBrush->SetColor(UIColor(255, 255, 255, 255));
                        ps.hCanvas->DrawLine(s_data.hBrush, ret.left + data_s->p_data.pots[2].x, ret.top + data_s->p_data.pots[2].y, ret.left + data_s->p_data.pots[3].x, ret.top + data_s->p_data.pots[3].y, 3, 0, TRUE);
                        ps.hCanvas->DrawLine(s_data.hBrush, ret.left + data_s->p_data.pots[4].x, ret.top + data_s->p_data.pots[4].y, ret.left + data_s->p_data.pots[3].x, ret.top + data_s->p_data.pots[3].y, 3, 0, TRUE);
                    }
                    else if ((ptr->dwStyle & rs_table_halfselect) != 0)
                    {
                        ps.hCanvas->DrawLine(s_data.hBrush, ret.left + data_s->p_data.pots[0].x, ret.top + data_s->p_data.pots[0].y, ret.left + data_s->p_data.pots[1].x, ret.top + data_s->p_data.pots[1].y, 3.f);
                        ps.hCanvas->DrawRoundRect(s_data.hBrush, ret.left, ret.top, ret.right, ret.bottom, 2, 1.f);
                    }
                    else
                        ps.hCanvas->DrawRoundRect(s_data.hBrush, ret.left, ret.top, ret.right, ret.bottom, 2, 1.f);

                }
                else
                {
                    UIColor crText = ptr->crText;
                    if (crText.empty())
                        GetColor(color_text_normal, crText);
                    RECT rcText{ nOffsetX + 3, 0, nOffsetX + nColWidth - 3, (INT)ps.uHeight };
                    if ((ps.dwState & state_down) != 0 && data_s->s_data.indexhit == i)
                        OffsetRect(&rcText, UIEngine::ScaleValue(1), UIEngine::ScaleValue(1));

                    UIImage* hImage = ptr->nImage;
                    if (ptr->pwzText)
                    {
                        FLOAT right = rcText.right;
                        if (fsort && (ptr->dwTextFormat & DT_RIGHT) != 0)
                        {
                            right -= 15;
                        }
                        //图标影响左边
                        FLOAT fleft = rcText.left;
                        if (hImage)
                        {
                            if (ptr->dwTextFormat & DT_CENTER)
                            {
                            }
                            else if (ptr->dwTextFormat & DT_RIGHT)
                            {
                            }
                            else
                            {
                                fleft = rcText.left + ptr->imgWidth + 5;
                            }
                        }
                        if (bShowAllwasy)
                        {
                            if (i != 1)
                            {
                                ps.hCanvas->DrawTextByColor(ps.hFont, ptr->pwzText, DT_SINGLELINE | ptr->dwTextFormat,
                                    fleft, rcText.top, right, rcText.bottom, crText, &ptr->ntextWidth);
                            }
                        }
                        else {
                            ps.hCanvas->DrawTextByColor(ps.hFont, ptr->pwzText, DT_SINGLELINE | ptr->dwTextFormat,
                                fleft, rcText.top, right, rcText.bottom, crText, &ptr->ntextWidth);
                        }
                        if (hImage)
                        {
                            if (ptr->dwTextFormat & DT_CENTER)
                            {
                                FLOAT left = rcText.left + (static_cast<float>(rcText.right) - rcText.left) / 2 - ptr->imgWidth - (ptr->ntextWidth / 2);
                                if (left > rcText.left)
                                    ps.hCanvas->DrawImage(hImage, left - 5, rcText.top + (static_cast<FLOAT>(rcText.bottom) - rcText.top - ptr->imgHeight) / 2, 255);
                            }
                            else if (ptr->dwTextFormat & DT_RIGHT)
                            {
                                FLOAT left = rcText.right - rcText.left - ptr->ntextWidth + UIEngine::ScaleValue(15);
                                if (left > rcText.left)
                                    ps.hCanvas->DrawImage(hImage, left, rcText.top + (rcText.bottom - rcText.top - ptr->imgHeight) / 2, 255);
                            }
                            else
                            {
                                ps.hCanvas->DrawImage(hImage, rcText.left + 2, rcText.top + (rcText.bottom - rcText.top - ptr->imgHeight) / 2, 255);
                            }
                        }
                        if (fsort)
                        {
                            if (ptr->ntextWidth + UIEngine::ScaleValue(40) < nColWidth)
                            {
                                FLOAT left = nOffsetX + ptr->ntextWidth + ptr->imgWidth + UIEngine::ScaleValue(10);
                                if ((ptr->dwTextFormat & DT_CENTER) != 0)
                                {
                                    left = nOffsetX + nColWidth / static_cast<float>(2) + ptr->ntextWidth / 2 + UIEngine::ScaleValue(5);
                                }
                                else if ((ptr->dwTextFormat & DT_RIGHT) != 0)
                                {
                                    left = rcText.right - UIEngine::ScaleValue(10);
                                }

                                if (data_s->s_data.fDesc_iCol == i)
                                {
                                    if (ptr->fDesc)
                                        s_data.hBrush->SetColor(UIColor(178, 178, 178, 255));
                                    else
                                        s_data.hBrush->SetColor(UIColor(0, 0, 0, 255));
                                }
                                else
                                    s_data.hBrush->SetColor(UIColor(178, 178, 178, 255));

                                ps.hCanvas->FillPoly(s_data.hBrush,
                                    left,
                                    (ps.uHeight - UIEngine::ScaleValue(8)) / 2 - UIEngine::ScaleValue(2),
                                    left + UIEngine::ScaleValue(8),
                                    (ps.uHeight - UIEngine::ScaleValue(8)) / 2 + UIEngine::ScaleValue(6),
                                    3, -90);
                                if (data_s->s_data.fDesc_iCol == i)
                                {
                                    if (!ptr->fDesc)
                                        s_data.hBrush->SetColor(UIColor(178, 178, 178, 255));
                                    else
                                        s_data.hBrush->SetColor(UIColor(0, 0, 0, 255));
                                }
                                else
                                    s_data.hBrush->SetColor(UIColor(178, 178, 178, 255));

                                ps.hCanvas->FillPoly(s_data.hBrush,
                                    left,
                                    (ps.uHeight - UIEngine::ScaleValue(8)) / 2 + UIEngine::ScaleValue(4),
                                    left + UIEngine::ScaleValue(8),
                                    (ps.uHeight - UIEngine::ScaleValue(8)) / 2 + UIEngine::ScaleValue(12),
                                    3, 90);

                            }
                            ptr->ntextWidth += UIEngine::ScaleValue(40);
                        }
                    }
                    else if (hImage)
                    {
                        ps.hCanvas->DrawImage(hImage, rcText.left + (nColWidth - ptr->imgWidth) / 2, rcText.top + (ps.uHeight - ptr->imgHeight) / 2, 255);
                    }

                }
                s_data.hBrush->SetColor(data_s->s_data.linecolour);
                ps.hCanvas->DrawLine(s_data.hBrush, nColWidth + nOffsetX, 0, nColWidth + nOffsetX, ps.uHeight, strokeWidth);
            }
            nOffsetX = nOffsetX + nColWidth;
        }

        if ((data_s->m_data.dwStyle & eos_table_drawhorizontalline) == eos_table_drawhorizontalline)
        {
            s_data.hBrush->SetColor(data_s->s_data.linecolour);
            ps.hCanvas->DrawLine(s_data.hBrush, strokeWidth, ps.uHeight - strokeWidth, ps.uWidth - strokeWidth, ps.uHeight - strokeWidth, strokeWidth, D2D1_DASH_STYLE_SOLID);
        }
    }
}


size_t HHBUI::UITable_Head::hittest(INT x, INT y, BOOL fJustHit, INT* rHitBlock)
{
    auto data_s = (UITable*)m_data.lParam;
    if ((m_data.dwState & state_allowdrag) == 0)
    {
        LPVOID pTCs = data_s->s_data.tcinfo;
        INT cTCs = data_s->s_data.ctcs;
        if (pTCs != 0 && cTCs > 0)
        {
            ExRectF rc{};
            GetRect(rc, grt_client, TRUE);
            rc.left = -data_s->GetScrollPos(TRUE);

            for (INT i = 1; i <= cTCs; i++) //循环检测列
            {
                auto ptr = (info_table_head*)((size_t)pTCs + (i - 1) * sizeof(info_table_head));
                INT nColWidth = ptr->nWidth;
                if (nColWidth > 0)
                {
                    rc.right = rc.left + nColWidth;

                    if ((ptr->dwStyle & cs_table_lockwidth) != cs_table_lockwidth) //如果为非锁定宽度列
                    {
                        rc.right = rc.right + 5;
                    }
                    if (rc.PtInRect(x,y)) //命中了某列
                    {
                        if (fJustHit == FALSE)
                        {
                            if (data_s->s_data.indexhit != i)
                            {
                                data_s->s_data.indexhit = i;
                                Redraw();
                            }
                        }
                       
                        if (rHitBlock != 0)
                        {
                            *rHitBlock = 0;
                            if (*rHitBlock == 0 && ((ptr->dwStyle & cs_table_lockwidth) != cs_table_lockwidth)) //线
                            {
                                auto rcBlock = rc;
                                rcBlock.right = rc.right;
                                rcBlock.left = rcBlock.right - 10;
                                if (rcBlock.PtInRect(x,y))
                                {
                                    if ((ptr->dwStyle & cs_table_checkbox) == cs_table_checkbox)
                                    {
                                        *rHitBlock = 3;
                                        break;
                                    }
                                    *rHitBlock = 2;
                                }
                            }
                        }
                        return i;
                    }
                    rc.left = rc.right;
                    if ((ptr->dwStyle & cs_table_lockwidth) != cs_table_lockwidth)
                    {
                        rc.left = rc.left - 5;
                    }
                }
            }
            if (fJustHit == FALSE)
            {
                if (data_s->s_data.indexhit != 0)
                {
                    data_s->s_data.indexhit = 0;
                    Redraw();
                }
            }
        }
    }
    else
    {
        *rHitBlock = 2;
        return data_s->s_data.indexhit;
    }
    return 0;
}

INT HHBUI::UITable::InsErtInfo(INT nInsertIndex, LPCWSTR pwzText, UINT nWidth, DWORD dwStyle, DWORD dwTextFormat, UIColor crText, UIColor crbk, UIColor crbk_hover, UIImage* nImage, bool draw)
{
    auto pInsertInfo = new info_table_head();
    if (pwzText)
        pInsertInfo->pwzText = StrDupW(pwzText);
    pInsertInfo->nInsertIndex = nInsertIndex;
    pInsertInfo->nWidth = UIEngine::ScaleValue(nWidth);
    pInsertInfo->dwStyle = dwStyle;
    pInsertInfo->dwTextFormat = dwTextFormat;
    pInsertInfo->crText = crText;
    pInsertInfo->crbk = crbk;
    if (crbk_hover.empty())
        crbk_hover = UIColor(204, 206, 219, 255);
    pInsertInfo->crbk_hover = crbk_hover;
    pInsertInfo->nImage = nImage;

    BOOL ballowmultiple = ((m_data.dwStyle & eos_table_allowmultiple) == eos_table_allowmultiple);
    if (ballowmultiple)
    {
        if (!s_data.itemcheckbox)
            s_data.itemcheckbox = (dwStyle & cs_table_checkbox);
        else
            FLAGS_DEL(dwStyle, cs_table_checkbox);
    }
    else
    {
        if ((dwStyle & cs_table_checkbox))
        {
            FLAGS_DEL(dwStyle, cs_table_checkbox);
            s_data.itemcheckbox = 0;
        }
    }

    INT nCount = s_data.ctcs;
    INT nIndexInsert = nInsertIndex;
    LPVOID pTCs = s_data.tcinfo;
    LPVOID pNew = __ptr_ins(&pTCs, nCount, &nIndexInsert, sizeof(info_table_head), pInsertInfo);
    info_table_head* ptc = (info_table_head*)((size_t)pNew + (nIndexInsert - 1) * sizeof(info_table_head));
    ptc->nInsertIndex = nIndexInsert;
    if (nImage != 0)
        nImage->GetSize(ptc->imgWidth, ptc->imgHeight);
    if (dwTextFormat == -1)
    {
        ptc->dwTextFormat = DT_VCENTER | DT_CENTER | DT_SINGLELINE | DT_WORD_ELLIPSIS;
    }
    nCount = nCount + 1;
    s_data.tcinfo = pNew;
    s_data.ctcs = nCount;
    for (size_t i = 0; i < s_data.itemList->size(); i++)
    {
        auto ptr = (info_table_head_s*)s_data.itemList->at(i);
        LPVOID pTDs = ptr->pTDInfo;
        LPVOID p2 = __ptr_ins(&pTDs, nCount, &nIndexInsert, sizeof(info_table_head_s), 0);
        ptr->pTDInfo = p2;
    }

    //需要从新排序
    s_data.fDesc_iCol = 0;
    if (draw)
        Update();
    return nIndexInsert;
}

void HHBUI::UITable::SetErtInfo(INT iCol, LPCWSTR pwzText, UINT nWidth, DWORD dwStyle,
    DWORD dwTextFormat, UIColor crText, UIColor crbk, UIColor crbk_hover, UIImage* nImage, bool draw)
{
    INT nCount = s_data.ctcs;
    if (iCol <= nCount && iCol > 0)
    {
        auto ptc = (info_table_head*)((size_t)s_data.tcinfo + (iCol - 1) * sizeof(info_table_head));
        if (pwzText)
        {
            ptc->pwzText = StrDupW(pwzText);
        }
        if (nImage)
        {
            delete ptc->nImage;
            ptc->nImage = nImage;
        }
        if (nWidth)
            ptc->nWidth = nWidth;
        if (dwStyle)
            ptc->dwStyle = dwStyle;
        if (dwTextFormat != -1)
            ptc->dwTextFormat = dwTextFormat;
        if (!crText.empty())
            ptc->crText = crText;
        if (!crbk.empty())
            ptc->crbk = crbk;
        if (!crbk_hover.empty())
            ptc->crbk_hover = crbk_hover;
        if (draw)
            Update();
    }
}

INT HHBUI::UITable::InsErtItem(INT nInsertIndex, DWORD dwStyle)
{
    auto pTR = new reportlistview_tr_s();
    pTR->dwStyle = dwStyle;
    pTR->nInsertIndex = nInsertIndex;
    LPVOID pTDs = nullptr;
    INT nCount = s_data.ctcs;
    if (nCount > 0)
    {
        pTDs = ExMemAlloc(sizeof(reportlistview_td_s) * nCount);
    }
    pTR->pTDInfo = pTDs;
    
    // 添加到所有项列表
    if (s_data.isPaging) {
        if (nInsertIndex == 0)
            s_data.allItems->emplace_back(pTR);
        else
            s_data.allItems->insert(s_data.allItems->begin() + nInsertIndex, pTR);
            
        // 刷新分页
        RefreshPaging();
        
        // 只有当前页的项才添加到显示列表
        INT totalPages = GetTotalPages();
        INT startIdx = (s_data.currentPage - 1) * s_data.pageSize;
        INT endIdx = s_data.currentPage * s_data.pageSize - 1;
        
        if (nInsertIndex >= startIdx && nInsertIndex <= endIdx) {
            INT visibleIndex = nInsertIndex - startIdx;
            if (visibleIndex < 0) visibleIndex = 0;
            
            if (visibleIndex == 0)
                s_data.itemList->emplace_back(pTR);
            else
                s_data.itemList->insert(s_data.itemList->begin() + visibleIndex, pTR);
                
            return visibleIndex + 1; // 返回在可见列表中的索引
        }
        
        // 如果不在当前页，返回在全部数据中的索引
        return (INT)s_data.allItems->size();
    }
    else {
        // 原有逻辑
        if (nInsertIndex == 0)
            s_data.itemList->emplace_back(pTR);
        else
            s_data.itemList->insert(s_data.itemList->begin() + nInsertIndex, pTR);
        return (INT)s_data.itemList->size();
    }
}

void HHBUI::UITable::SetItem(INT iRow, INT iCol, LPCWSTR pwzText, UIColor crText, UIColor crbk, UIImage* nImage, DWORD dwStyle, LPARAM lParam)
{
    if (IndexCheck(iRow - 1))
    {
        auto pTD = (reportlistview_td_s*)GetInfo(iRow - 1, iCol);
        if (pTD)
        {
            if (!pTD->dwMerge || pTD->iRow == iRow && pTD->iCol == iCol)
            {
                if (pwzText)
                {
                    pTD->wzText = StrDupW(pwzText);
                }
                if (!crText.empty())
                {
                    pTD->crText = crText;
                }
                if (!crbk.empty())
                {
                    pTD->crbk = crbk;
                }
                if (lParam)
                {
                    pTD->lParam = lParam;
                }
                if (nImage)
                {
                    delete pTD->nImageIndex;
                    pTD->nImageIndex = nImage;
                    nImage->GetSize(pTD->imgWidth, pTD->imgHeight);
                }
                if (dwStyle != 0)
                {
                    pTD->dwStyle = dwStyle;
                }
            }
        }
    }
}

void HHBUI::UITable::GetItem(INT iRow, INT iCol, tableItem** item)
{
    if (IndexCheck(iRow - 1))
    {
        auto pTD = (reportlistview_td_s*)GetInfo(iRow - 1, iCol);
        if (pTD)
        {
            *item = new tableItem();
            RtlMoveMemory(*item, pTD, sizeof(tableItem));
        }
    }
}

void HHBUI::UITable::Update()
{
    if (s_data.objhead)
        s_data.objhead->Update();
    
    // 更新itemCount，考虑分页
    INT nCount = 0;
    if (s_data.isPaging) {
        // 计算当前页应该显示的项数
        nCount = (INT)s_data.itemList->size();
    } else {
        nCount = (INT)s_data.itemList->size();
    }
    
    SetItemCount(nCount, LVSICF_NOSCROLL);
    UpdateIndex(1, nCount);
    Redraw();
    
    // 如果启用分页，确保分页控件位置正确
    if (s_data.isPaging && s_data.page != nullptr) {
        // 重新设置分页控件的位置
        s_data.page->Move(s_data.pageX, s_data.pageY, s_data.pageWidth, s_data.pageHeight, TRUE);
    }
}

void HHBUI::UITable::Sort(INT iCol, BOOL fDesc)
{
    if (iCol >= 0 && iCol <= s_data.ctcs)
    {
        auto ptr = (info_table_head*)__ptr_index(s_data.tcinfo, s_data.ctcs, iCol, sizeof(info_table_head));
        if (ptr)
        {
            ptr->fDesc = !fDesc;

            INT bSortablenType = 0;
            if ((ptr->dwStyle & cs_table_sortable_ab) != 0)
                bSortablenType = cs_table_sortable_ab;
            else if ((ptr->dwStyle & cs_table_sortable_number) != 0)
                bSortablenType = cs_table_sortable_number;
      
            auto vec = reinterpret_cast<std::vector<reportlistview_tr_s*>*>(s_data.itemList);
            std::sort(vec->begin(), vec->end(), [iCol, fDesc, bSortablenType](reportlistview_tr_s* a, reportlistview_tr_s* b) {
                return CompareByIRow(a, b, iCol, fDesc, bSortablenType);
                });
            s_data.fDesc_iCol = iCol;
            s_data.down_move = FALSE;
            s_data.iCol_start = 0;
            s_data.iRow_start = 0;
            s_data.iCol_end = 0;
            s_data.iRow_end = 0;
            Redraw();
        }
    }
}

void HHBUI::UITable::SetCrSelect(UIColor cr)
{
    s_data.select = cr;
}

void HHBUI::UITable::GetSelectIndex(INT& iRow_start, INT& iRow_end, INT& iCol_start, INT& iCol_end)
{
    iRow_start = s_data.iRow_start;
    iRow_end = s_data.iRow_end;
    iCol_start = s_data.iCol_start;
    iCol_end = s_data.iCol_end;
}

void HHBUI::UITable::SetSelectIndex(INT iRow_start, INT iRow_end, INT iCol_start, INT iCol_end)
{
    s_data.iRow_start = iRow_start;
    s_data.iRow_end = iRow_end;
    s_data.iCol_start = iCol_start;
    s_data.iCol_end = iCol_end;
}

void HHBUI::UITable::SetMergeIndex(INT iRow_start, INT iRow_end, INT iCol_start, INT iCol_end)
{
    iCol_end = (iCol_end <= 0) ? iCol_start : iCol_end;
    iRow_end = (iRow_end <= 0) ? iRow_start : iRow_end;

    for (INT i = iRow_start; i <= iRow_end; i++)
    {
        for (INT j = iCol_start; j <= iCol_end; j++)
        {
            auto pTC = (info_table_head*)((size_t)s_data.tcinfo + (j - 1) * sizeof(info_table_head));
            auto pTD = (reportlistview_td_s*)GetInfo(i - 1, j);
            if (!pTC || !pTD)
                continue;
            pTD->dwMerge = TRUE;
            pTD->dwMergebottom = (i == iRow_end);
            pTD->dwMergeright = (j == iCol_end);

            if (j != iCol_start || i != iRow_start && pTD->wzText)
            {
                LocalFree((LPVOID)pTD->wzText);
                pTD->wzText = nullptr;

                if (pTD->nImageIndex)
                {
                    delete pTD->nImageIndex;
                    pTD->nImageIndex = nullptr;
                }
                pTD->iCol = iCol_start;
                pTD->iRow = iRow_start;
            }
            else if (j == iCol_start && i == iRow_start)
            {
                pTD->iCol = iCol_start;
                pTD->iRow = iRow_start;
                pTD->iendCol = iCol_end;
                pTD->iendRow = iRow_end;
            }

        }
    }
    s_data.down_move = FALSE;
    s_data.iCol_start = 0;
    s_data.iRow_start = 0;
    SetSelect(0);
    Redraw();
}

void HHBUI::UITable::SetDecomPoseIndex(INT iRow_start, INT iRow_end, INT iCol_start, INT iCol_end)
{
    for (INT i = iRow_start; i <= iRow_end; i++)
    {
        for (INT j = iCol_start; j <= iCol_end; j++)
        {
            auto pTD = (reportlistview_td_s*)GetInfo(i - 1, j);
            if (pTD)
            {
                pTD->dwMerge = FALSE;
                pTD->dwMergebottom = FALSE;
                pTD->dwMergeright = FALSE;
                pTD->iRow = 0;
                pTD->iendRow = 0;
                pTD->iCol = 0;
                pTD->iendCol = 0;
            }
        }
    }
    s_data.down_move = FALSE;
    s_data.iCol_start = 0;
    s_data.iRow_start = 0;
    Redraw();
}

BOOL HHBUI::UITable::DeleteItem(INT iRow, bool draw)
{
    iRow -= 1;
    if (s_data.isPaging) {
        INT actualRow = iRow;
        if (actualRow < 0 || actualRow >= (INT)s_data.itemList->size())
            return FALSE;
            
        // 获取在全部数据中的索引
        INT startIdx = (s_data.currentPage - 1) * s_data.pageSize;
        INT actualIndex = startIdx + actualRow;
        
        // 检查索引是否有效
        if (!IndexCheck(actualIndex))
            return FALSE;
            
        // 删除allItems中的项
        arr_del(actualIndex);
        s_data.allItems->erase(s_data.allItems->begin() + actualIndex);
        
        // 删除当前显示列表中的项
        s_data.itemList->erase(s_data.itemList->begin() + actualRow);
        
        // 重置选择状态
        s_data.down_move = FALSE;
        s_data.iCol_start = 0;
        s_data.iRow_start = 0;
        s_data.nOffsetXOld = 1;
        s_data.nOffsetYOld = 1;
        if (iRow == GetSelect())
            SetSelect(0);
            
        // 刷新分页
        RefreshPaging();
        
        if (draw)
            Update();
            
        return TRUE;
    }
    else {
        // 原有逻辑
        if (IndexCheck(iRow))
        {
            arr_del(iRow);
            s_data.down_move = FALSE;
            s_data.iCol_start = 0;
            s_data.iRow_start = 0;
            s_data.nOffsetXOld = 1;
            s_data.nOffsetYOld = 1;
            if (iRow == GetSelect())
                SetSelect(0);
            s_data.itemList->erase(s_data.itemList->begin() + iRow);
            if (draw)
                Update();
            return TRUE;
        }
    }
    return FALSE;
}

void HHBUI::UITable::DeleteAllItem()
{
    // 清空当前显示列表
    for (size_t i = 0; i < s_data.itemList->size(); i++)
    {
        arr_del(i);
    }
    s_data.itemList->clear();
    
    // 如果启用了分页，也清空所有项列表
    if (s_data.isPaging) {
        for (size_t i = 0; i < s_data.allItems->size(); i++)
        {
            // 这里不需要调用arr_del，因为已经在上面的循环中删除了
            // 除非某些项不在当前页
            if (i >= s_data.itemList->size()) {
                arr_del(i);
            }
        }
        s_data.allItems->clear();
    }
    
    s_data.down_move = FALSE;
    s_data.fDesc_iCol = 0;
    s_data.iCol_start = 0;
    s_data.iRow_start = 0;
    s_data.nOffsetXOld = 1;
    s_data.nOffsetYOld = 1;
    SetSelect(0);
    
    // 刷新分页
    if (s_data.isPaging) {
        RefreshPaging();
    }
    
    Update();
}

void HHBUI::UITable::DeleteColumn(INT iCol, bool draw)
{
    INT nCount = s_data.ctcs;
    if (iCol <= nCount && iCol > 0)
    {
        auto ptc = (info_table_head*)((size_t)s_data.tcinfo + (iCol - 1) * sizeof(info_table_head));
        if (ptc->nImage)
            delete ptc->nImage;
        if ((ptc->dwStyle & cs_table_checkbox))
            s_data.itemcheckbox = FALSE;
        __ptr_del(&s_data.tcinfo, nCount, iCol, sizeof(info_table_head));
        nCount = nCount - 1;
        s_data.ctcs = nCount;
        size_t arraySize = s_data.itemList->size();
        for (size_t i = 0; i < arraySize; i++)//删除列清除该列行内容
        {
            auto ptr = static_cast<reportlistview_tr_s*>(s_data.itemList->at(i));
            LPVOID pTDs = ptr->pTDInfo;
            auto nImage = ((reportlistview_td_s*)((size_t)pTDs + (iCol - 1) * sizeof(reportlistview_td_s)))->nImageIndex;
            if (nImage)
                delete nImage;
            __ptr_del(&pTDs, nCount + 1, iCol, sizeof(reportlistview_td_s));
            ptr->pTDInfo = pTDs;
        }
        s_data.fDesc_iCol = 0;
    }
    if (nCount == 0)
        s_data.itemList->clear();
    if (draw)
        Update();
}

void HHBUI::UITable::DeleteAllColumn()
{
    INT nCount = s_data.ctcs;
    LPVOID pTCs = s_data.tcinfo;
    for (INT i = 0; i < nCount; i++)
    {
        auto pTC = (info_table_head*)((size_t)pTCs + i * sizeof(info_table_head));
        if (pTC->nImage)
            delete pTC->nImage;
        if ((pTC->dwStyle & cs_table_checkbox))
            s_data.itemcheckbox = FALSE;
    }
    ExMemFree(pTCs);
    s_data.tcinfo = 0;
    s_data.ctcs = 0;
    reportlistview_tr_s* ptr = nullptr;
    LPVOID pTDs = nullptr;
    size_t arraySize = s_data.itemList->size();
    for (size_t i = 0; i < arraySize; i++)
    {
        auto ptr = static_cast<reportlistview_tr_s*>(s_data.itemList->at(i));
        pTDs = ptr->pTDInfo;
        for (INT j = 0; j < nCount; j++)
        {
            auto pTD = ((reportlistview_td_s*)((size_t)pTDs + j * sizeof(reportlistview_td_s)));
            if (pTD && pTD->nImageIndex)
                delete pTD->nImageIndex;
        }
        ExMemFree(pTDs);
        ptr->pTDInfo = 0;
    }
    s_data.nOffsetXOld = 1;
    s_data.nOffsetYOld = 1;
    s_data.fDesc_iCol = 0;
    s_data.itemList->clear();
    Update();
}

INT HHBUI::UITable::GetColumnCount()
{
    return s_data.ctcs;
}
void HHBUI::UITable::GetItemCheck(size_t iRow, BOOL& fCheck)
{
    iRow -= 1;
    if (IndexCheck((int)iRow))
    {
        auto pTR = static_cast<reportlistview_tr_s*>(s_data.itemList->at(iRow));
        if (pTR)
        {
            fCheck = (pTR->dwStyle & rs_table_checkboxok) != 0;
        }
    }
}
void HHBUI::UITable::SetItemCheck(size_t iRow, BOOL fCheck)
{
    iRow -= 1;
    if (IndexCheck((int)iRow))
    {
        auto pTR = static_cast<reportlistview_tr_s*>(s_data.itemList->at(iRow));
        if (pTR)
        {
            if (fCheck)
            {
                if ((pTR->dwStyle & rs_table_checkboxok) != rs_table_checkboxok)
                    pTR->dwStyle |= rs_table_checkboxok;
            }
            else
                pTR->dwStyle &= ~rs_table_checkboxok;
        }
    }
}
void HHBUI::UITable::SetHeadHeight(INT ndheight)
{
    s_data.headheight = UIEngine::ScaleValue(ndheight);
    OnUpdate(m_data.Frame.right - m_data.Frame.left, m_data.Frame.bottom - m_data.Frame.top);
}
LRESULT HHBUI::UITable::OnPsProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
    if (uMsg == WM_DESTROY)
    {
        DeleteAllItem();
        delete s_data.hBrush;
        delete s_data.itemList;
        delete s_data.allItems; // 释放分页数据存储
        if (s_data.dpath)
            delete s_data.dpath;
        if (s_data.page)
            delete s_data.page; // 释放分页控件
    }
    else if (uMsg == WM_SIZE)
    {
        OnUpdate(LOWORD(lParam), HIWORD(lParam));
    }
    else if (uMsg == WM_VSCROLL || uMsg == WM_HSCROLL)
    {
        if (uMsg == WM_HSCROLL)
        {
            s_data.objhead->Redraw();
        }
        
        // 如果有分页控件，确保它的位置不变
        if (s_data.isPaging && s_data.page != nullptr) {
            s_data.page->Move(s_data.pageX, s_data.pageY, s_data.pageWidth, s_data.pageHeight, FALSE);
        }
    }
    else if (uMsg == WM_LBUTTONDOWN || uMsg == WM_LBUTTONDBLCLK)
    {
        INT iRow = s_data.iRow_end, iCol = s_data.iCol_end;
        if (iRow == 0)
            iRow = GetHotItem();
        if (iCol == 0)
            iCol = GetHitcol(GET_X_LPARAM(lParam));
        if ((m_data.dwStyle & eos_table_allowediting))
        {
            if (uMsg == WM_LBUTTONDBLCLK)
            {
                auto pTD = (reportlistview_td_s*)GetInfo(iRow - 1, iCol);
                if (pTD != 0)
                {
                    if (pTD->dwMerge)
                    {
                        iCol = pTD->iCol;
                        iRow = pTD->iRow;
                    }

                    auto ptc = (info_table_head*)__ptr_index(s_data.tcinfo, s_data.ctcs, iCol, sizeof(info_table_head));
                    if (ptc)
                    {
                        if (ptc->dwStyle & cs_table_checkbox)
                            return 0;
                        if (pTD->dwStyle & es_table_disableed)
                            return 0;
                    }
                    s_data.objedit->SetText(pTD->wzText);
                    s_data.iCol_move = iCol;
                    s_data.iRow_move = iRow;
                    s_data.down_move = FALSE;
                    s_data.iCol_start = 0;
                    s_data.iRow_start = 0;
                    s_data.objedit->Show(TRUE);
                    s_data.objedit->SetFocus();
                    UIColor crbackground;
                    GetColor(color_background, crbackground);
                    if (crbackground.empty())
                        crbackground = UIColor(255, 255, 255, 255);
                    s_data.objedit->SetColor(color_background, crbackground);

                    Redraw();
                    return 0;
                }
            }

        }
        if (uMsg == WM_LBUTTONDOWN)
        {
            BOOL Sok = FALSE;
            s_data.iCol_start = 0;
            s_data.iRow_start = 0;
            s_data.down_move = TRUE;
            if (s_data.iCol_move != 0)
            {
                Edit_Killfous(this, s_data.objedit, wParam, lParam);
                s_data.objedit->KillFocus();
                return TRUE;
            }
    
            if (iRow <= 0)
                return FALSE;
            auto pTR = static_cast<reportlistview_tr_s*>(s_data.itemList->at(iRow - 1));
            if (pTR)
            {
                auto ptc = (info_table_head*)__ptr_index(s_data.tcinfo, s_data.ctcs, iCol, sizeof(info_table_head));
                if (ptc)
                {
                    ExRectF rc{};
                    GetItemRect(iRow, rc);
                    rc.right = ptc->nWidth;
                    rc.bottom += rc.bottom - rc.top;
                    // 命中检查框
                    if (rc.PtInRect( GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam)))
                    {
                        if ((pTR->dwStyle & rs_table_checkboxok))
                        {
                            FLAGS_DEL(pTR->dwStyle, rs_table_checkboxok);
                            auto pTCs = (info_table_head*)((size_t)s_data.tcinfo + (iCol - 1) * sizeof(info_table_head));
                            FLAGS_DEL(pTCs->dwStyle, rs_table_checkboxok);
                            //FLAGS_ADD(pTCs->dwStyle, rs_table_halfselect);
                        }
                        else
                        {
                            FLAGS_ADD(pTR->dwStyle, rs_table_checkboxok);
                            INT uicont = 0;
                            for (size_t i = 0; i < s_data.itemList->size(); i++)
                            {
                                auto ptr = (info_table_head_s*)s_data.itemList->at(i);
                                if ((ptr->dwStyle & rs_table_checkboxok) != 0)
                                {
                                    uicont += 1;
                                }
                            }
                            if (uicont == s_data.itemList->size())
                            {
                                auto pTCs = (info_table_head*)((size_t)s_data.tcinfo + (iCol - 1) * sizeof(info_table_head));
                                //FLAGS_DEL(pTCs->dwStyle, rs_table_halfselect);
                                FLAGS_ADD(pTCs->dwStyle, rs_table_checkboxok);
                            }
                        }
                        s_data.objhead->Redraw();
                        Sok = TRUE;
                    }
                }
                INT iKey = m_data.pWnd->GetKeys();
                if ((iKey & VK_CONTROL) != 0 && (iKey & VK_SHIFT) == 0)//ctrl按下 shift没有按下
                {
                    if ((pTR->dwStyle & rs_table_checkboxok))
                    {
                        FLAGS_DEL(pTR->dwStyle, rs_table_checkboxok);
                    }
                    else
                    {
                        FLAGS_ADD(pTR->dwStyle, rs_table_checkboxok);
                    }
                    s_data.IsCtrl = TRUE;
                    Sok = TRUE;
                }
                else if (!s_data.itemcheckbox && s_data.IsCtrl)
                {
                    //_rlv_checkboxok(data_s, nIndexTC, FALSE);
                    s_data.IsCtrl = FALSE;
                    Sok = TRUE;
                }
                if (Sok)
                {
                    DispatchNotify(WMM_RLVN_CHECK, iRow, (pTR->dwStyle & rs_table_checkboxok) ? 1 : 0);
                    Redraw();
                }
            }
        }
    }
    else if (uMsg == WM_LBUTTONUP)
    {
        if (s_data.down_move && s_data.iCol_end == 0 && s_data.iRow_end == 0)
        {
            s_data.iCol_start = 0;
            s_data.iRow_start = 0;
        }
        s_data.down_move = FALSE;
    }
    else if (uMsg == WM_MOUSEMOVE)
    {
        INT nIndexTR = GetHotItem();
        INT nIndexTC = GetHitcol(GET_X_LPARAM(lParam));
        if (s_data.down_move)
        {
            if (nIndexTC < s_data.iCol_start && nIndexTR < s_data.iRow_start)
            {
                s_data.down_move = FALSE;
                s_data.iCol_start = 0;
                s_data.iRow_start = 0;
            }
            else if (s_data.iCol_start == 0 && s_data.iRow_start == 0)
            {
                s_data.iCol_start = nIndexTC;
                s_data.iRow_start = nIndexTR;
            }
        }
        else if (s_data.iCol_start != 0 && s_data.iRow_start != 0)
        {
            Redraw();
            
            // 确保分页控件位置不变
            if (s_data.isPaging && s_data.page != nullptr) {
                s_data.page->Move(s_data.pageX, s_data.pageY, s_data.pageWidth, s_data.pageHeight, FALSE);
            }
            
            return 0;
        }
        if (nIndexTC == s_data.iCol_start && nIndexTR == s_data.iRow_start)
        {
            s_data.iCol_end = 0;
            s_data.iRow_end = 0;
        }
        else
        {
            s_data.iCol_end = nIndexTC;
            s_data.iRow_end = nIndexTR;
        }
        Redraw();
        
        // 确保分页控件位置不变
        if (s_data.isPaging && s_data.page != nullptr) {
            s_data.page->Move(s_data.pageX, s_data.pageY, s_data.pageWidth, s_data.pageHeight, FALSE);
        }
    }
    else if (uMsg == WM_MOUSELEAVE)
    {
        if ((m_data.dwStyle & eos_table_showtable))
        {
            if (s_data.iCol_start == 0 && s_data.iRow_start == 0)
            {
                s_data.iCol_end = 0;
                s_data.iRow_end = 0;
                Redraw();
            }
        }
    }
    else if (uMsg == WM_KEYDOWN)
    {
        if (wParam == VK_DELETE)
        {
            if (s_data.iCol_start == 0 && s_data.iRow_start == 0 || s_data.iRow_end == 0 && s_data.iCol_end == 0)
                return 0;
            for (INT i = s_data.iRow_start; i <= s_data.iRow_end; i++)
            {
                for (INT j = s_data.iCol_start; j <= s_data.iCol_end; j++)
                {
                   auto pTC = (info_table_head*)((size_t)s_data.tcinfo + (j - 1) * sizeof(info_table_head));
                   auto pTD = (reportlistview_td_s*)GetInfo(i - 1, j);
                    if ((pTD->dwStyle & es_table_disableed) != es_table_disableed && pTD)
                    {
                        delete pTD->nImageIndex;
                        pTD->nImageIndex = 0;
                        //清除合并信息
                        pTD->dwMerge = FALSE;
                        pTD->dwMergebottom = FALSE;
                        pTD->dwMergeright = FALSE;
                        pTD->iCol = 0;
                        pTD->iRow = 0;
                        pTD->iendCol = 0;
                        pTD->iendRow = 0;
                    }

                }
            }
            Redraw();
        }
    }
    return S_OK;
}
BOOL HHBUI::UITable::OnPsCustomDraw(INT iItem, ps_customdraw ps)
{
    if (iItem <= 0 || iItem > static_cast<INT>(s_data.itemList->size())) {
        return FALSE;
    }
    const INT nCount = s_data.ctcs;
    auto pTR = static_cast<reportlistview_tr_s*>(s_data.itemList->at(iItem - 1));
    if (!pTR || !s_data.tcinfo) {
        return FALSE;
    }
   
    auto pTC = static_cast<info_table_head*>(s_data.tcinfo);
    auto rcTD = ps.rcPaint;
    for (INT iCol = 1; iCol <= nCount; ++iCol)
    {
        auto pTD = static_cast<reportlistview_td_s*>(GetInfo(iItem - 1, iCol));
        if (!pTD) continue;
        rcTD.top = ps.rcPaint.top;
        rcTD.right = rcTD.left + pTC->nWidth;
        HandleCellMerging(pTC, pTD, pTR, rcTD, ps, iItem, iCol);
        rcTD.left = rcTD.right;
        pTC = reinterpret_cast<info_table_head*>(reinterpret_cast<std::uintptr_t>(pTC) + sizeof(info_table_head));
    }

    return TRUE;
}

BOOL HHBUI::UITable::OnPsDraw(ps_context ps)
{
    UIColor crb;
    GetColor(color_border, crb);
    if (!crb.empty())
    {
        auto rcPaint = m_data.Frame_d;
        auto headheight = s_data.headheight;
       
        if (s_data.dpath && !m_data.radius.empty())
        {
            s_data.hBrush->SetColor(crb);
            ps.hCanvas->DrawPath(s_data.hBrush, s_data.dpath, 1.5f, D2D1_DASH_STYLE_SOLID);
        }
        else
        {
            ps.hCanvas->DrawRect(s_data.hBrush, rcPaint.left, rcPaint.top, rcPaint.right, rcPaint.bottom, 1, D2D1_DASH_STYLE_SOLID);
        }
        if (rcPaint.top < headheight && (ps.dwStyle & eos_table_nohead) != eos_table_nohead)
        {
            ps.hCanvas->SetClipRect(rcPaint.left, headheight, rcPaint.right, rcPaint.bottom + headheight);
            return TRUE;
        }
    }
    return FALSE;
}

void HHBUI::UITable::HandleCellMerging(const LPVOID pTC, const LPVOID pTD, const LPVOID pTR, ExRectF rcTD, const ps_customdraw ps, INT iItem, INT iCol)
{
    auto pTCn = (info_table_head*)pTC;
    auto pTDn = (reportlistview_td_s*)pTD;
    auto pTRn = (reportlistview_tr_s*)pTR;
    const BOOL bShowAlways = (ps.dwStyle & eos_table_showtable);
    const BOOL bAllowMultiple = (ps.dwStyle & eos_table_allowmultiple);
    const BOOL fCheckbox = (pTCn->dwStyle & cs_table_checkbox);
    const FLOAT strokeWidth = static_cast<FLOAT>(s_data.linewidth);


    ExRectF kCheckboxBaseRect{ 0.f, 0.f, UIEngine::ScaleValue(16), UIEngine::ScaleValue(16) };
    constexpr FLOAT kCheckboxRounding = 2.0f;
    constexpr FLOAT kTextPadding = 3.0f;
    // 处理单元格合并
    if (pTDn->dwMerge && iCol == pTDn->iCol && iItem == pTDn->iRow)
    {
        // 列合并计算
        UINT mergedWidth = 0;
        for (INT j = pTDn->iCol; j <= pTDn->iendCol; ++j)
        {
            auto colInfo = reinterpret_cast<info_table_head*>(
                reinterpret_cast<size_t>(s_data.tcinfo) + (j - 1) * sizeof(info_table_head));
            mergedWidth += colInfo->nWidth;
        }
        rcTD.right = rcTD.left + mergedWidth;
        // 行合并计算
        UINT mergedHeight = ps.rcPaint.top;
        for (INT k = pTDn->iRow; k <= pTDn->iendRow; ++k)
        {
            mergedHeight += ps.rcPaint.bottom - ps.rcPaint.top;
        }
        rcTD.bottom = mergedHeight;
    }

    if (!pTDn->crbk.empty() && !pTDn->dwMerge)
    {
        s_data.hBrush->SetColor(pTDn->crbk);
        ps.hCanvas->FillRect(s_data.hBrush, rcTD.left, rcTD.top, rcTD.right, rcTD.bottom);
    }
    // 复选框处理
    if (fCheckbox && !bShowAlways && bAllowMultiple)
    {
        const RECT checkRect = {
            static_cast<LONG>(rcTD.left + (pTCn->nWidth - kCheckboxBaseRect.right) / 2),
            static_cast<LONG>(rcTD.top + (rcTD.bottom - rcTD.top - kCheckboxBaseRect.bottom) / 2),
            static_cast<LONG>(rcTD.left + (pTCn->nWidth - kCheckboxBaseRect.right) / 2 + kCheckboxBaseRect.right),
            static_cast<LONG>(rcTD.top + (rcTD.bottom - rcTD.top - kCheckboxBaseRect.bottom) / 2 + kCheckboxBaseRect.bottom)
        };

        s_data.hBrush->SetColor(s_data.select);
        const ExRectF ret(checkRect.left - 1, checkRect.top,
            checkRect.right - checkRect.left,
            checkRect.bottom - checkRect.top, TRUE);

        if ((pTRn->dwStyle & rs_table_checkboxok) != 0)
        {
            ps.hCanvas->FillRoundRect(s_data.hBrush, ret.left, ret.top, ret.right, ret.bottom, 2);
            s_data.hBrush->SetColor(UIColor(255, 255, 255, 255));
            ps.hCanvas->DrawLine(s_data.hBrush, ret.left + p_data.pots[2].x, ret.top + p_data.pots[2].y, ret.left + p_data.pots[3].x, ret.top + p_data.pots[3].y, 3, 0, TRUE);
            ps.hCanvas->DrawLine(s_data.hBrush, ret.left + p_data.pots[4].x, ret.top + p_data.pots[4].y, ret.left + p_data.pots[3].x, ret.top + p_data.pots[3].y, 3, 0, TRUE);
        }
        else
        {
            ps.hCanvas->DrawRoundRect(s_data.hBrush, ret.left, ret.top, ret.right, ret.bottom, 2, 1.f);
        }
    }
    else
    {
        UIImage* hImage = pTDn->nImageIndex;
        UIColor crText = pTDn->crText;
        if (crText.empty())
            GetColor(color_text_normal, crText);
        UIColor count_color;
        if (ps.dwState & state_select)
        {
            GetColor(color_text_down, crText);
            if (crText.empty())
                crText = pTDn->crText;
            count_color = s_data.Color[1];
        }
        else if (ps.dwState & state_hover)
        {
            GetColor(color_text_hover, crText);
            if (crText.empty())
                crText = pTDn->crText;
            count_color = s_data.Color[0];
        }
        INT _nIndexTC = s_data.iCol_end;
        INT _nIndexTR = s_data.iRow_end;
        INT iRow = iItem;
        //处于编辑状态
        if ((ps.dwStyle & eos_table_allowediting) != 0)
        {
            if (iCol == s_data.iCol_move && iRow == s_data.iRow_move && !fCheckbox)
            {
                s_data.objedit->SetPos(rcTD.left + UIEngine::ScaleValue(1), rcTD.top, (rcTD.right - rcTD.left) - UIEngine::ScaleValue(2),
                    rcTD.bottom - rcTD.top, 0, SWP_NOZORDER | SWP_NOACTIVATE | SWP_NOOWNERZORDER | SWP_ASYNCWINDOWPOS | SWP_EX_NODPISCALE);
            }

        }
        if (s_data.iCol_start != 0 && s_data.iRow_start != 0)//选区合并
        {
            INT x = 0, y = 0;
            INT nWidth = 0, nHeight = 0;
            if (s_data.iCol_start == iCol)
            {
                x = rcTD.left;
            }
            if (s_data.iRow_start == iRow)
            {
                y = rcTD.top;
            }
            for (INT j = s_data.iCol_start; j <= s_data.iCol_end; j++)
            {
                auto upTC = (info_table_head*)((size_t)s_data.tcinfo + (j - 1) * sizeof(info_table_head));

                nWidth += upTC->nWidth;

            }
            for (INT i = s_data.iRow_start; i <= s_data.iRow_end; i++)
            {
                nHeight += rcTD.bottom - rcTD.top + 1;
            }
            if (x != 0 && y != 0 && nWidth != 0 && nHeight != 0)
            {
                s_data.hBrush->SetColor(UIColor(175, 218, 255, 255));
                ps.hCanvas->FillRect(s_data.hBrush, x + 1, y, x + nWidth, y + nHeight - 1);
                s_data.hBrush->SetColor(UIColor(14, 142, 254, 255));
                ps.hCanvas->DrawRect(s_data.hBrush, x + 1, y, x + nWidth - 1, y + nHeight - 2, 1.f);
            }
        }
        if (bShowAlways && !pTDn->dwMerge)
        {
            BOOL ornIndex = (iCol == _nIndexTC && iRow == _nIndexTR);
            UIColor text_hover;
            GetColor(color_text_hover, text_hover);
            s_data.hBrush->SetColor(text_hover);
            if (ornIndex && iCol != 1)
            {
                ps.hCanvas->DrawRect(s_data.hBrush, rcTD.left, rcTD.top + 1, rcTD.right, rcTD.bottom, 1, 0);

            }
            else if (ornIndex && iCol == 1)
            {
                ps.hCanvas->FillRect(s_data.hBrush, rcTD.left, rcTD.top, rcTD.right, rcTD.bottom);
            }

        }
        else if (!pTDn->dwMerge && s_data.iCol_start == 0 && s_data.iRow_start == 0 && s_data.iCol_move == 0 && s_data.iRow_move == 0)
        {
            if (bAllowMultiple && (pTRn->dwStyle & rs_table_checkboxok) != 0 && !s_data.itemcheckbox)
            {
                s_data.hBrush->SetColor(s_data.Color[1]);
                ps.hCanvas->FillRect(s_data.hBrush, rcTD.left, rcTD.top, rcTD.right + 1, rcTD.bottom + 1);

            }
            else if (!count_color.empty())
            {
                s_data.hBrush->SetColor(count_color);
                ps.hCanvas->FillRect(s_data.hBrush, rcTD.left, rcTD.top - 1, rcTD.right + 1, rcTD.bottom + 1);
            }
        }
        LPCWSTR wzText = pTDn->wzText;
        if (wzText)
        {
            FLOAT left = rcTD.left + 3;
            if (hImage)
            {
                if (pTCn->dwTextFormat & DT_CENTER)
                    left = rcTD.left + 5;
                else
                    left = rcTD.left + pTDn->imgWidth + 5;
            }
            FLOAT iWidth = 0.f, iHeight = 0.f;
            ps.hCanvas->DrawTextByColor(ps.hFont, wzText, pTCn->dwTextFormat, left, rcTD.top + 2, rcTD.right - 3, rcTD.bottom - 2, crText, &iWidth, &iHeight);

            if (hImage)
            {
                if (pTCn->dwTextFormat & DT_CENTER)
                {
                    left = rcTD.left + (static_cast<float>(rcTD.right) - rcTD.left) / 2 - pTDn->imgWidth - (iWidth / 2);
                    if (left > rcTD.left)
                        ps.hCanvas->DrawImage(hImage, left, rcTD.top + (static_cast<FLOAT>(rcTD.bottom) - rcTD.top - pTDn->imgHeight) / 2, 255);
                }
                else if (pTCn->dwTextFormat & DT_RIGHT)
                {
                    left = rcTD.right - rcTD.left - iWidth + UIEngine::ScaleValue(15);
                    if (left > rcTD.left)
                        ps.hCanvas->DrawImage(hImage, left, rcTD.top + (rcTD.bottom - rcTD.top - pTDn->imgHeight) / 2, 255);
                }
                else
                {
                    left = rcTD.left + 3;
                    ps.hCanvas->DrawImage(hImage, left, rcTD.top + (rcTD.bottom - rcTD.top - pTDn->imgHeight) / 2, 255);
                }
            }

        }
        else
        {
            ps.hCanvas->DrawImage(hImage, rcTD.left + (rcTD.right - rcTD.left - pTDn->imgWidth) / 2, rcTD.top + (rcTD.bottom - rcTD.top - pTDn->imgHeight) / 2, 255);
        }
    }
    s_data.hBrush->SetColor(s_data.linecolour);
    if ((ps.dwStyle & eos_table_drawverticalline) == eos_table_drawverticalline && !pTDn->dwMerge || pTDn->dwMergeright)
    {
        ps.hCanvas->DrawLine(s_data.hBrush, rcTD.right, rcTD.top, rcTD.right, rcTD.bottom, strokeWidth);
    }
    if ((ps.dwStyle & eos_table_drawhorizontalline) == eos_table_drawhorizontalline && !pTDn->dwMerge || pTDn->dwMergebottom)
    {
        ps.hCanvas->DrawLine(s_data.hBrush, rcTD.left, rcTD.bottom, rcTD.right, rcTD.bottom, strokeWidth);

    }

}

void HHBUI::UITable::OnCreate()
{
    s_data.itemwidth = 0;
    s_data.itemheight = UIEngine::ScaleValue(35);
    s_data.headheight = 0;
    s_data.linewidth = 1;
    s_data.linecolour = UIColor(213, 214, 214, 255);
    s_data.tcinfo = 0;
    s_data.hBrush = new UIBrush();
    s_data.itemList = new std::vector<LPVOID>();
    s_data.allItems = new std::vector<LPVOID>();
    if ((m_data.dwStyle & eos_table_nohead) != eos_table_nohead)
    {
        s_data.headheight = UIEngine::ScaleValue(35);
        s_data.objhead = new UITable_Head(this, 0, 0, 100, 35);
        s_data.objhead->SetlParam((size_t)this);
       
    }
    s_data.objedit = new UIEdit(this, 0, 0, m_data.Frame_d.right, 30, NULL, eos_hidden | eos_edit_hideselection, eos_ex_focusable, 0, DT_VCENTER | DT_CENTER | DT_SINGLELINE);
    s_data.objedit->SetEvent(WMM_KEYDOWN, OnEdit_Enter_Event);
    s_data.objedit->SetlParam((size_t)this);
    m_data.dwFlags |= EOF_NOBORDER;
    
    // 初始化分页相关
    s_data.isPaging = FALSE;
    s_data.pageSize = 10;
    s_data.currentPage = 1;
    s_data.page = nullptr;
    
    // 如果设置了分页风格，则启用分页
    if (m_data.dwStyle & eos_table_pageable) {
        EnablePaging(TRUE);
    }
}

void HHBUI::UITable::OnUpdate(INT nWidth, INT nHeight)
{
    s_data.nOffsetXOld = -123321;
    s_data.nOffsetYOld = -321123;
    if (s_data.objhead)
    {
        m_data.Frame_c.top = s_data.headheight;
        s_data.objhead->Move(0, 1, nWidth / UIWinApi::ToList.drawing_default_dpi, s_data.headheight / UIWinApi::ToList.drawing_default_dpi, FALSE);
        s_data.objhead->SetRadius(m_data.radius.left, m_data.radius.top, 0, 0);
    }
    if (!m_data.radius.empty())
    {
        if (s_data.dpath)
            delete s_data.dpath;
        s_data.dpath = new UIPath();
        s_data.dpath->BeginPath();
        if (s_data.dpath->StartFigure(0, 0) == S_OK)
        {
            s_data.dpath->AddCustomRoundRect(1.f, 1.f, nWidth - 1, nHeight - 1,
                m_data.radius.left, m_data.radius.top, m_data.radius.right, m_data.radius.bottom);
        }
        s_data.dpath->EndPath();
    }
    
    // 如果启用分页，更新分页控件的位置
    if (s_data.isPaging && s_data.page != nullptr) {
        // 获取表格当前位置
        ExRectF rc;
        GetRect(rc, grt_window, TRUE);
        
        // 更新分页控件的默认位置（保持在表格下方居中）
        s_data.pageX = rc.left;
        s_data.pageY = rc.bottom + UIEngine::ScaleValue(10);
        s_data.pageWidth = rc.right - rc.left;
        
        // 移动分页控件
        s_data.page->Move(s_data.pageX, s_data.pageY, s_data.pageWidth, s_data.pageHeight, TRUE);
    }
}

bool HHBUI::UITable::IndexCheck(INT index)
{
    if (index < (INT)s_data.itemList->size() && index >= 0)
        return true;
    return false;
}

LPVOID HHBUI::UITable::GetInfo(INT iRow, INT iCol)
{
    reportlistview_td_s* pTD = nullptr;
    INT nCount = s_data.ctcs;
    if (iCol <= 0 || iCol > nCount)
    {
        return pTD;
    }
    auto pTR = (reportlistview_tr_s*)s_data.itemList->at(iRow);
    if (pTR)
    {
        LPVOID pTDs = pTR->pTDInfo;
        if (pTDs)
        {
            pTD = (reportlistview_td_s*)((size_t)pTDs + (iCol - 1) * sizeof(reportlistview_td_s));
        }
    }
    return pTD;
}

INT HHBUI::UITable::GetHitcol(INT x)
{
    LPVOID pTCs = s_data.tcinfo;
    if (pTCs != 0 && s_data.ctcs > 0)
    {
        INT nOffsetX = -GetScrollPos(TRUE);
        for (INT i = 1; i <= s_data.ctcs; i++)
        {
            auto ptr = (info_table_head*)((size_t)pTCs + (i - 1) * sizeof(info_table_head));
            INT nColWidth = ptr->nWidth;
            if (nColWidth > 0)
            {
                if (x >= nOffsetX && x < nOffsetX + nColWidth)
                {
                    return i;
                }
            }
            nOffsetX = nOffsetX + nColWidth;
        }
    }
    return 0;
}

void HHBUI::UITable::arr_del(size_t nIndex)
{
    auto pTR = (reportlistview_tr_s*)s_data.itemList->at(nIndex);
    if (pTR != 0)
    {
        LPVOID pTDs = pTR->pTDInfo;
        if (pTDs != 0)
        {
            for (INT i = 0; i < s_data.ctcs; i++)
            {
                auto pTD = (reportlistview_td_s*)((size_t)pTDs + i * sizeof(reportlistview_td_s));
             
                if (pTD->nImageIndex)
                    delete pTD->nImageIndex;
            }
            ExMemFree(pTDs);
        }
    }
}

bool HHBUI::UITable::CompareByIRow(LPVOID a, LPVOID b, size_t iCol, BOOL fDesc, INT bSortablenType)
{
    std::wstring textA;
    std::wstring textB;
    auto aa = (reportlistview_tr_s*)a;
    auto bb = (reportlistview_tr_s*)b;
    if (aa->pTDInfo != 0)
    {
        auto pTD = (reportlistview_td_s*)((size_t)aa->pTDInfo + (iCol - 1) * sizeof(reportlistview_td_s));
        if (pTD->wzText == 0)
            return false;
        textA = pTD->wzText;
    }
    if (bb->pTDInfo != 0)
    {
        auto pTD = (reportlistview_td_s*)((size_t)bb->pTDInfo + (iCol - 1) * sizeof(reportlistview_td_s));
        if (pTD->wzText == 0)
            return false;
        textB = pTD->wzText;
    }
    // 根据 fDesc 决定升序还是降序
    if (!fDesc) // 降序
    {
        if (bSortablenType == cs_table_sortable_ab)
        {
            return textA > textB;
        }
        else if (bSortablenType == cs_table_sortable_number)
        {
            double numA = 0.0, numB = 0.0;
            std::wstringstream(textA) >> numA;
            std::wstringstream(textB) >> numB;
            return numA > numB;
        }
        return false;
    }
    else // 升序
    {
        if (bSortablenType == cs_table_sortable_ab)
        {
            return textA < textB;
        }
        else if (bSortablenType == cs_table_sortable_number)
        {
            double numA = 0.0, numB = 0.0;
            std::wstringstream(textA) >> numA;
            std::wstringstream(textB) >> numB;
            return numA < numB;
        }
        return false;
    }
    return false;
}

void HHBUI::UITable::Edit_Killfous(LPVOID p, UIEdit* edit, WPARAM wParam, LPARAM lParam)
{
    auto data_s = (UITable*)p;
    if (edit->IsTextModify())
    {
        auto pTD = (reportlistview_td_s*)data_s->GetInfo(data_s->s_data.iRow_move - 1, data_s->s_data.iCol_move);
        if (pTD)
        {
            pTD->wzText = StrDupW(edit->GetText());
            data_s->DispatchNotify(WMM_RLVN_CENTCHANGE, data_s->s_data.iRow_move, data_s->s_data.iCol_move);
        }
    }
    data_s->s_data.iRow_move = 0;
    data_s->s_data.iCol_move = 0;
    edit->Show(FALSE);
    data_s->Redraw();
}

LRESULT HHBUI::UITable::OnEdit_Enter_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
    if (nCode == WMM_KEYDOWN)
    {
        if (wParam == 13)
        {
            auto edit = (UIEdit*)UIView;
            Edit_Killfous((UITable*)edit->GetlParam(), edit, wParam, lParam);
            return S_FALSE;
        }
    }
    return S_OK;
}

// 实现分页相关方法
void HHBUI::UITable::EnablePaging(BOOL enable, INT pageSize, INT x, INT y, INT width, INT height)
{
    // 如果状态没有变化，则不做任何处理
    if (s_data.isPaging == enable && s_data.pageSize == pageSize && s_data.page != nullptr)
        return;
    
    s_data.isPaging = enable;
    s_data.pageSize = pageSize;
    
    if (enable) {
        // 如果之前没有创建分页控件，则创建
        if (s_data.page == nullptr) {
            auto parent = GetParent();
            ExRectF rc;
            GetRect(rc, grt_window, TRUE);
            
            // 默认位置在表格下方，水平居中
            if (x == 0 && y == 0) {
                x = rc.left;
                y = rc.bottom + UIEngine::ScaleValue(10);
                width = rc.right - rc.left;
                height = UIEngine::ScaleValue(30);
            }
            
            // 保存分页控件的位置信息，用于后续更新
            s_data.pageX = x;
            s_data.pageY = y;
            s_data.pageWidth = width;
            s_data.pageHeight = height;
            
            auto newPage = new UIPage(parent, x, y, width, height, NULL, 
                eos_paging | eos_paging_pnarrow | eos_paging_showtips | eos_paging_showjump);
            
            // 设置分页控件样式
            newPage->SetStyle(e_page_center);
            newPage->SetCrText(UIColor(51, 51, 51, 255), UIColor(0, 108, 190, 255), UIColor(255, 255, 255, 255));
            newPage->SetCrbkg({}, UIColor(235, 235, 235, 255), UIColor(0, 108, 190, 255));
            newPage->SetCrBorder({}, UIColor(235, 235, 235, 255), UIColor(0, 108, 190, 255));
            newPage->SetBtnRound(4);
            newPage->SetEvent(WMM_PAGE_SELCHANGER, OnPage_Change_Event);
            newPage->SetlParam((size_t)this);
            
            s_data.page = newPage;
        }
        else {
            // 恢复分页控件的位置
            s_data.page->Move(s_data.pageX, s_data.pageY, s_data.pageWidth, s_data.pageHeight);
            s_data.page->Show(TRUE);
        }
        
        // 如果原来没有启用分页，则将当前的itemList复制到allItems
        if (!s_data.allItems->size() && s_data.itemList->size()) {
            s_data.allItems->assign(s_data.itemList->begin(), s_data.itemList->end());
        }
        
        // 刷新分页
        RefreshPaging();
    }
    else {
        // 如果禁用分页，则将所有项显示出来
        if (s_data.allItems->size()) {
            s_data.itemList->clear();
            s_data.itemList->assign(s_data.allItems->begin(), s_data.allItems->end());
        }
        
        // 隐藏分页控件
        if (s_data.page != nullptr) {
            s_data.page->Show(FALSE);
        }
    }
    
    Update();
}

// 将GetPageControl函数放在类定义之外，作为全局函数
HHBUI::UIPage* HHBUI::UITable::GetPageControl()
{
    return s_data.page;
}

void HHBUI::UITable::RefreshPaging()
{
    if (!s_data.isPaging || s_data.page == nullptr)
        return;
    
    // 计算总页数
    INT totalItems = (INT)s_data.allItems->size();
    INT totalPages = (totalItems + s_data.pageSize - 1) / s_data.pageSize;
    
    // 更新分页控件
    s_data.page->SetCount(totalItems, s_data.pageSize);
    s_data.page->SetCurrent(s_data.currentPage);
    
    // 更新当前显示列表
    s_data.itemList->clear();
    
    // 计算当前页的起始和结束索引
    INT startIdx = (s_data.currentPage - 1) * s_data.pageSize;
    INT endIdx = std::min(startIdx + s_data.pageSize, totalItems);
    
    // 添加当前页的项到显示列表
    for (INT i = startIdx; i < endIdx; i++) {
        s_data.itemList->push_back(s_data.allItems->at(i));
    }
    
    // 确保分页控件位置不变
    s_data.page->Move(s_data.pageX, s_data.pageY, s_data.pageWidth, s_data.pageHeight, FALSE);
}

void HHBUI::UITable::GoToPage(INT page)
{
    if (!s_data.isPaging || page < 1)
        return;
    
    INT totalPages = GetTotalPages();
    if (page > totalPages)
        page = totalPages;
    
    if (s_data.currentPage != page) {
        s_data.currentPage = page;
        RefreshPaging();
        Update();
        
        // 发送页码改变事件
        DispatchNotify(WMM_RLVN_PAGE_CHANGE, page, 0);
    }
    
    if (s_data.page)
        s_data.page->SetCurrent(page);
}

INT HHBUI::UITable::GetTotalPages()
{
    if (!s_data.isPaging)
        return 1;
    
    INT totalItems = (INT)s_data.allItems->size();
    return (totalItems + s_data.pageSize - 1) / s_data.pageSize;
}

LRESULT HHBUI::UITable::OnPage_Change_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
    if (nCode == WMM_PAGE_SELCHANGER) {
        // 通过UIView获取页面控件，并从lParam获取对应的表格对象
        auto pageCtrl = (UIPage*)UIView;
        auto table = (UITable*)pageCtrl->GetlParam();
        INT page = (INT)lParam;
        
        // 设置当前页并刷新表格
        table->GoToPage(page);
        
        return S_OK;
    }
    return S_OK;
}

