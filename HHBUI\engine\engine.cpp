﻿/**
** =====================================================================================
**
**       文件名称: engine.cpp
**       创建时间: 2025-08-03 (优化版本)
**       文件描述: 【HHBUI】引擎核心管理系统 - 现代化C++17引擎架构框架 （实现文件）
**
**       主要功能:
**       - 现代化引擎生命周期管理实现
**       - 智能DPI感知与缩放系统实现
**       - 高性能资源管理与初始化实现
**       - 异常安全的引擎状态管理实现
**       - 跨平台兼容性支持实现
**       - 调试与性能监控集成实现
**
**       技术特性:
**       - 采用现代C++17标准与RAII管理
**       - 异常安全保证与错误恢复机制
**       - 智能指针与自动资源管理
**       - 高性能DPI缩放算法
**       - 线程安全的状态管理
**       - 实时性能监控与调试诊断
**
** =====================================================================================
**/

#include "pch.h"
#include "engine.h"
#include "common/winapi.h"
#include "common/Exception.h"
#include <algorithm>
#include <sstream>

namespace HHBUI
{
	// 静态成员变量定义
	std::atomic<EngineState> UIEngine::s_engine_state{ EngineState::UNINITIALIZED };
	std::mutex UIEngine::s_state_mutex;
	std::chrono::steady_clock::time_point UIEngine::s_init_time;
	EngineInitConfig UIEngine::s_current_config;
	EngineStats UIEngine::s_stats;
	std::function<void(const std::string&)> UIEngine::s_error_callback;
	std::function<void(const std::string&)> UIEngine::s_debug_callback;

	namespace
	{
		/// 检查是否为Windows 10或更高版本
		[[nodiscard]] bool IsWindows10OrGreater() noexcept
		{
			try
			{
				using RtlGetVersionProc = void(WINAPI*)(DWORD*, DWORD*, DWORD*);

				HMODULE ntdll = GetModuleHandleW(L"ntdll.dll");
				if (!ntdll)
					return false;

				auto rtl_get_version = reinterpret_cast<RtlGetVersionProc>(
					GetProcAddress(ntdll, "RtlGetNtVersionNumbers"));

				if (!rtl_get_version)
					return false;

				DWORD major, minor, build;
				rtl_get_version(&major, &minor, &build);

				return (major > 10) || (major == 10 && minor >= 0);
			}
			catch (...)
			{
				return false;
			}
		}

		/// 现代化DPI管理器
		class DPIManager
		{
		public:
			static HRESULT Initialize(const EngineInitConfig& config, float& dpi_scale_x, float& dpi_scale_y) noexcept
			{
				try
				{
					HDC screen_dc = GetDC(nullptr);
					if (!screen_dc)
						return E_FAIL;

					// 获取系统DPI
					const float system_dpi_x = static_cast<float>(GetDeviceCaps(screen_dc, LOGPIXELSX));
					const float system_dpi_y = static_cast<float>(GetDeviceCaps(screen_dc, LOGPIXELSY));
					ReleaseDC(nullptr, screen_dc);

					// 计算DPI缩放比例
					dpi_scale_x = system_dpi_x / USER_DEFAULT_SCREEN_DPI;
					dpi_scale_y = system_dpi_y / USER_DEFAULT_SCREEN_DPI;

					// 应用自定义DPI缩放
					if (config.custom_dpi_scale > 0.0f)
					{
						dpi_scale_x = config.custom_dpi_scale;
						dpi_scale_y = config.custom_dpi_scale;
					}

					// 限制最大缩放比例
					dpi_scale_x = std::min(dpi_scale_x, 2.0f);
					dpi_scale_y = std::min(dpi_scale_y, 2.0f);

					// 量化到0.25的倍数以减少渲染误差
					dpi_scale_x = std::round(dpi_scale_x * 4.0f) / 4.0f;
					dpi_scale_y = std::round(dpi_scale_y * 4.0f) / 4.0f;

					// 存储到全局状态
					UIWinApi::ToList.CapsdpiX = system_dpi_x;
					UIWinApi::ToList.CapsdpiY = system_dpi_y;
					UIWinApi::ToList.drawing_default_dpi = dpi_scale_x;

					return S_OK;
				}
				catch (...)
				{
					return E_FAIL;
				}
			}
		};
	}

	// ==================== EngineInitConfig 实现 ====================

	bool EngineInitConfig::IsValid() const noexcept
	{
		// 基本验证
		if (device_index < -1)
			return false;

		if (custom_dpi_scale < 0.0f || custom_dpi_scale > 5.0f)
			return false;

		if (default_font_size < 6 || default_font_size > 72)
			return false;

		return true;
	}
	// ==================== UIEngine 核心实现 ====================

	HRESULT UIEngine::Initialize(const EngineInitConfig* config) noexcept
	{
		try
		{
			// 检查当前状态
			EngineState expected = EngineState::UNINITIALIZED;
			if (!s_engine_state.compare_exchange_strong(expected, EngineState::INITIALIZING, std::memory_order_acq_rel))
			{
				LogError("Engine is already initialized or in process of initialization");
				return S_FALSE;
			}

			std::lock_guard<std::mutex> lock(s_state_mutex);

			// 使用默认配置或提供的配置
			s_current_config = config ? *config : EngineInitConfig{};

			// 验证配置
			if (!s_current_config.IsValid())
			{
				LogError("Invalid engine configuration provided");
				s_engine_state.store(EngineState::ERROR_STATE, std::memory_order_release);
				return E_INVALIDARG;
			}

			// 记录初始化开始时间
			s_init_time = std::chrono::steady_clock::now();

			// 初始化COM
			HRESULT hr = CoInitialize(nullptr);
			if (FAILED(hr))
			{
				LogError("Failed to initialize COM: " + std::to_string(hr));
				s_engine_state.store(EngineState::ERROR_STATE, std::memory_order_release);
				return hr;
			}

			// 设置DPI感知
			hr = InitializeDPI(s_current_config);
			if (FAILED(hr))
			{
				LogError("Failed to initialize DPI management");
				CoUninitialize();
				s_engine_state.store(EngineState::ERROR_STATE, std::memory_order_release);
				return hr;
			}

			// 初始化图形系统
			hr = InitializeGraphics(s_current_config);
			if (FAILED(hr))
			{
				LogError("Failed to initialize graphics system");
				CoUninitialize();
				s_engine_state.store(EngineState::ERROR_STATE, std::memory_order_release);
				return hr;
			}

			// 初始化字体系统
			hr = InitializeFonts(s_current_config);
			if (FAILED(hr))
			{
				LogError("Failed to initialize font system");
				CoUninitialize();
				s_engine_state.store(EngineState::ERROR_STATE, std::memory_order_release);
				return hr;
			}

			// 初始化统计信息
			s_stats.init_time = s_init_time;
			s_stats.current_dpi_scale = UIWinApi::ToList.drawing_default_dpi;
			UpdateStats();

			// 设置引擎状态为已初始化
			s_engine_state.store(EngineState::INITIALIZED, std::memory_order_release);

			LogDebug("Engine initialized successfully");
			return S_OK;
		}
		catch (const std::exception& e)
		{
			LogError("Exception during engine initialization: " + std::string(e.what()));
			s_engine_state.store(EngineState::ERROR_STATE, std::memory_order_release);
			return E_FAIL;
		}
		catch (...)
		{
			LogError("Unknown exception during engine initialization");
			s_engine_state.store(EngineState::ERROR_STATE, std::memory_order_release);
			return E_FAIL;
		}
	}

	HRESULT UIEngine::Shutdown() noexcept
	{
		try
		{
			EngineState expected = EngineState::INITIALIZED;
			if (!s_engine_state.compare_exchange_strong(expected, EngineState::SHUTTING_DOWN, std::memory_order_acq_rel))
			{
				// 如果引擎未初始化，直接返回成功
				if (expected == EngineState::UNINITIALIZED)
					return S_OK;

				LogError("Engine is not in a state that can be shut down");
				return S_FALSE;
			}

			std::lock_guard<std::mutex> lock(s_state_mutex);

			// 清理资源
			if (UIWinApi::ToList.default_font)
			{
				delete UIWinApi::ToList.default_font;
				UIWinApi::ToList.default_font = nullptr;
			}

			// 清理图标资源
			if (UIWinApi::ToList.hIcon)
			{
				DestroyIcon(UIWinApi::ToList.hIcon);
				UIWinApi::ToList.hIcon = nullptr;
			}

			if (UIWinApi::ToList.hIconsm)
			{
				DestroyIcon(UIWinApi::ToList.hIconsm);
				UIWinApi::ToList.hIconsm = nullptr;
			}

			// 关闭子系统
			UIWinApi::UnInit();
			UIDrawContext::UnInit();
			CoUninitialize();

			// 重置引擎实例
			UIWinApi::ToList.engine_instance = nullptr;

			// 重置状态
			s_engine_state.store(EngineState::UNINITIALIZED, std::memory_order_release);

			LogDebug("Engine shut down successfully");
			return S_OK;
		}
		catch (const std::exception& e)
		{
			LogError("Exception during engine shutdown: " + std::string(e.what()));
			s_engine_state.store(EngineState::ERROR_STATE, std::memory_order_release);
			return E_FAIL;
		}
		catch (...)
		{
			LogError("Unknown exception during engine shutdown");
			s_engine_state.store(EngineState::ERROR_STATE, std::memory_order_release);
			return E_FAIL;
		}
	}
	// ==================== UIEngine 状态查询方法 ====================

	EngineState UIEngine::GetState() noexcept
	{
		return s_engine_state.load(std::memory_order_acquire);
	}

	bool UIEngine::IsInitialized() noexcept
	{
		return GetState() == EngineState::INITIALIZED;
	}

	bool UIEngine::IsDebugMode() noexcept
	{
		return s_current_config.debug_mode;
	}

	float UIEngine::ScaleValue(float value) noexcept
	{
		if (UIWinApi::ToList.drawing_default_dpi > 1.0f)
		{
			return std::round(value * UIWinApi::ToList.drawing_default_dpi);
		}
		return value;
	}

	float UIEngine::GetDPIScale() noexcept
	{
		return UIWinApi::ToList.drawing_default_dpi;
	}

	float UIEngine::GetUptime() noexcept
	{
		if (GetState() == EngineState::UNINITIALIZED)
			return 0.0f;

		auto now = std::chrono::steady_clock::now();
		auto duration = std::chrono::duration_cast<std::chrono::duration<float>>(now - s_init_time);
		return duration.count();
	}

	std::wstring_view UIEngine::GetVersion() noexcept
	{
		return HHBUI_VERSION;
	}

	EngineStats UIEngine::GetStats() noexcept
	{
		std::lock_guard<std::mutex> lock(s_state_mutex);
		UpdateStats();
		return s_stats;
	}

	void UIEngine::SetErrorCallback(std::function<void(const std::string&)> callback) noexcept
	{
		std::lock_guard<std::mutex> lock(s_state_mutex);
		s_error_callback = std::move(callback);
	}

	void UIEngine::SetDebugCallback(std::function<void(const std::string&)> callback) noexcept
	{
		std::lock_guard<std::mutex> lock(s_state_mutex);
		s_debug_callback = std::move(callback);
	}

	void UIEngine::ForceGarbageCollection() noexcept
	{
		// 实现垃圾回收逻辑
		// 这里可以添加内存清理、资源回收等操作
	}

	void UIEngine::ResetStats() noexcept
	{
		std::lock_guard<std::mutex> lock(s_state_mutex);
		s_stats = EngineStats{};
		s_stats.init_time = s_init_time;
		s_stats.current_dpi_scale = UIWinApi::ToList.drawing_default_dpi;
	}

	// ==================== UIEngine 内部辅助方法 ====================

	HRESULT UIEngine::InitializeDPI(const EngineInitConfig& config) noexcept
	{
		try
		{
			// 检测Windows版本并设置DPI感知
			UIWinApi::ToList.dwMajorVersion = IsWindows10OrGreater();

			if (config.enable_per_monitor_dpi && UIWinApi::ToList.dwMajorVersion)
			{
				SetThreadDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);
			}
			else
			{
				SetProcessDPIAware();
			}

			// 初始化DPI管理
			float dpi_scale_x, dpi_scale_y;
			return DPIManager::Initialize(config, dpi_scale_x, dpi_scale_y);
		}
		catch (...)
		{
			return E_FAIL;
		}
	}

	HRESULT UIEngine::InitializeGraphics(const EngineInitConfig& config) noexcept
	{
		try
		{
			// 获取应用程序实例
			HINSTANCE app_instance = config.app_instance;
			if (!app_instance)
			{
				app_instance = GetModuleHandleW(nullptr);
			}

			// 初始化WinAPI系统
			HRESULT hr = UIWinApi::Init(app_instance);
			if (FAILED(hr))
				return hr;

			// 初始化绘图上下文
			int device_index = static_cast<int>(config.device_index);
			hr = UIDrawContext::Init(device_index);
			if (FAILED(hr))
				return hr;

			// 设置应用程序图标
			if (app_instance)
			{
				TCHAR file_path[MAX_PATH + 1];
				GetModuleFileName(app_instance, file_path, MAX_PATH);

				UIWinApi::ToList.hIcon = ExtractIconW(app_instance, file_path, 0);
				UIWinApi::ToList.hIconsm = ExtractIconW(app_instance, file_path, 0);
				UIWinApi::ToList.engine_instance = app_instance;
			}

			// 注册窗口类
			UIWnd::RegClass(L"Hhbui.WindowClass.UI", 0, 0);

			return S_OK;
		}
		catch (...)
		{
			return E_FAIL;
		}
	}
	HRESULT UIEngine::InitializeFonts(const EngineInitConfig& config) noexcept
	{
		try
		{
			// 获取系统默认字体
			UIWinApi::ToList.drawing_default_fontLogFont = new LOGFONTW();
			SystemParametersInfoW(SPI_GETICONTITLELOGFONT, sizeof(LOGFONTW),
				UIWinApi::ToList.drawing_default_fontLogFont, false);

			// 应用配置中的字体设置
			if (!config.default_font_family.empty())
			{
				wcsncpy_s(UIWinApi::ToList.drawing_default_fontLogFont->lfFaceName,
					config.default_font_family.c_str(), LF_FACESIZE - 1);
			}

			// 设置字体大小（应用DPI缩放）
			UIWinApi::ToList.drawing_default_fontLogFont->lfHeight =
				-static_cast<LONG>(ScaleValue(static_cast<float>(config.default_font_size)));

			// 设置字体样式
			UIWinApi::ToList.drawing_default_fontLogFont->lfWeight =
				(static_cast<uint32_t>(config.default_font_style) & static_cast<uint32_t>(FontStyle::BOLD)) ? FW_BOLD : FW_NORMAL;
			UIWinApi::ToList.drawing_default_fontLogFont->lfItalic =
				(static_cast<uint32_t>(config.default_font_style) & static_cast<uint32_t>(FontStyle::ITALIC)) ? TRUE : FALSE;
			UIWinApi::ToList.drawing_default_fontLogFont->lfUnderline =
				(static_cast<uint32_t>(config.default_font_style) & static_cast<uint32_t>(FontStyle::UNDERLINE)) ? TRUE : FALSE;
			UIWinApi::ToList.drawing_default_fontLogFont->lfStrikeOut =
				(static_cast<uint32_t>(config.default_font_style) & static_cast<uint32_t>(FontStyle::STRIKEOUT)) ? TRUE : FALSE;

			// 创建默认字体对象
			UIWinApi::ToList.default_font = new UIFont();

			return S_OK;
		}
		catch (...)
		{
			return E_FAIL;
		}
	}

	void UIEngine::UpdateStats() noexcept
	{
		try
		{
			auto now = std::chrono::steady_clock::now();
			s_stats.uptime = std::chrono::duration_cast<std::chrono::milliseconds>(now - s_init_time);
			s_stats.current_dpi_scale = UIWinApi::ToList.drawing_default_dpi;

			// 这里可以添加更多统计信息的更新
			// 例如：内存使用量、活跃窗口数量等
		}
		catch (...)
		{
			// 静默处理异常
		}
	}

	void UIEngine::LogError(const std::string& message) noexcept
	{
		try
		{
			if (s_error_callback)
			{
				s_error_callback(message);
			}
			else if (s_current_config.debug_mode)
			{
				OutputDebugStringA(("[HHBUI ERROR] " + message + "\n").c_str());
			}
		}
		catch (...)
		{
			// 静默处理异常
		}
	}

	void UIEngine::LogDebug(const std::string& message) noexcept
	{
		try
		{
			if (s_debug_callback)
			{
				s_debug_callback(message);
			}
			else if (s_current_config.debug_mode)
			{
				OutputDebugStringA(("[HHBUI DEBUG] " + message + "\n").c_str());
			}
		}
		catch (...)
		{
			// 静默处理异常
		}
	}

	// ==================== 兼容性方法实现 ====================

	HRESULT UIEngine::Init(info_Init* info)
	{
		// 转换旧式配置到新式配置
		EngineInitConfig config;

		if (info)
		{
			config.device_index = info->device;
			config.app_instance = info->hInstance;
			config.custom_dpi_scale = info->dwScaledpi;
			config.debug_mode = info->dwDebug != FALSE;

			if (info->default_font_Face)
				config.default_font_family = info->default_font_Face;

			config.default_font_size = info->default_font_Size;
			config.default_font_style = static_cast<FontStyle>(info->default_font_Style);
		}

		return Initialize(&config);
	}

	HRESULT UIEngine::UnInit()
	{
		return Shutdown();
	}

	BOOL UIEngine::QueryDebug()
	{
		return IsDebugMode() ? TRUE : FALSE;
	}

	BOOL UIEngine::QueryInit()
	{
		return IsInitialized() ? TRUE : FALSE;
	}

	FLOAT UIEngine::fScale(FLOAT n)
	{
		return ScaleValue(n);
	}

	FLOAT UIEngine::GetDefaultScale()
	{
		return GetDPIScale();
	}

	FLOAT UIEngine::GetTime()
	{
		return GetUptime();
	}

	LPCWSTR UIEngine::GetVersionLegacy()
	{
		static std::wstring version_str = std::wstring(GetVersion());
		return version_str.c_str();
	}

} // namespace HHBUI