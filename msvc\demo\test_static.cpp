﻿#include "hhbui.h"
using namespace HHBUI;
LRESULT CALLBACK OnDragdropMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (uMsg == WM_EX_DROP)//先处理eos_ex_dragdrop
	{
		info_dropinfo dropinfo{ 0 };
		RtlMoveMemory(&dropinfo, (LPVOID)lParam, sizeof(info_dropinfo));

		if (obj->CheckDropFormat(dropinfo.pDataObject, CF_UNICODETEXT) == S_OK || obj->CheckDropFormat(dropinfo.pDataObject, CF_TEXT) == S_OK) //判断是不是文本
		{
			INT len = obj->CheckDropString(dropinfo.pDataObject, 0, 0);
			if (len > 0)
			{
				std::wstring str;
				str.resize(len * 2 + 2);
				obj->CheckDropString(dropinfo.pDataObject, (LPWSTR)str.c_str(), len + 1);
				output(L"收到文本:", str);
				return 1;
			}
		}
	}
	else if (uMsg == WM_DROPFILES) //此消息处理拖放文件
	{
		UINT fileNumber = obj->CheckDropFileNumber(lParam);
		output(fileNumber);
		for (UINT index = 0; index < fileNumber; index++)
		{
			UINT fileNameLength = obj->CheckDragQueryFile(lParam, index, NULL, 0);
			if (fileNameLength)
			{
				std::wstring fileName;
				fileName.resize(fileNameLength);
				obj->CheckDragQueryFile(lParam, index, (LPWSTR)fileName.c_str(), fileNameLength + 2);
				output(L"收到文件:", fileName);
			}
		}

		return 1;
	}

	return S_OK;
}
void teststatic(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 600, 500, L"hello Static", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);
	window->Layout_Init(elt_absolute);

	auto bstatic = new UIStatic(window, 20, 70, 300, 100, L"我是一个允许拖放文件的标签~", 0, eos_ex_dragdrop | eos_ex_acceptfiles);
	bstatic->SetColor(color_background, UIColor(230, 231, 232, 255));
	bstatic->SetColor(color_border, UIColor(194, 195, 201, 255));
	bstatic->SetMsgProc(OnDragdropMsgProc);

	auto bstatic_pic = new UIStatic(window, 340, 70, 200, 200, L"我还可以当图片框~");
	bstatic_pic->SetColor(color_text_normal, UIColor(255, 255, 255, 255));
	bstatic_pic->SetColor(color_border, UIColor(0, 108, 190, 255));
	bstatic_pic->SetRadius(15, 15, 15, 15);
	window->Layout_Absolute_Setedge(bstatic_pic, elcp_absolute_left, elcp_absolute_type_px, 340);
	window->Layout_Absolute_Setedge(bstatic_pic, elcp_absolute_top, elcp_absolute_type_px, 70);
	window->Layout_Absolute_Setedge(bstatic_pic, elcp_absolute_right, elcp_absolute_type_px, 20);
	window->Layout_Absolute_Setedge(bstatic_pic, elcp_absolute_bottom, elcp_absolute_type_px, 30);

	LPVOID imgdata;
	size_t retSize = 0;
	UIreadFile(LR"(icons\\1.jpg)", imgdata, retSize);
	bstatic_pic->SetBackgImage(imgdata, retSize, 0, 0, 0, 0, bif_disablescale);

	auto bstatic1 = new UIStatic(window, 20, 190, 300, 50, L"我是一个分割线标签~", eos_static_dline);
	bstatic1->SetLineWidth(2);
	bstatic1->SetLineColor(UIColor(0, 108, 190, 255));

	auto bstatic2 = new UIStatic(window, 20, 240, 300, 30, L"我是一个支持长文本自动滚动的标签~支持自定义绘制内容", eos_static_roll);
	bstatic2->SetColor(color_border, UIColor(194, 195, 201, 255));

	auto bstatic3 = new UIStatic(window, 20, 280, 300, 30, L"我是一个支持动态模糊的标签", eos_static_blurtext);
	bstatic3->SetColor(color_text_normal, UIColor(0, 153, 255, 255));

	auto bstatic4 = new UIStatic(window, 20, 320, 300, 30, L"我是一个高性能标签🤭🙂😊😘😁", eos_static_pro);
	bstatic4->SetColor(color_text_normal, UIColor(112, 132, 255, 255));

	auto bstatic5 = new UIStatic(window, 20, 380, 300, 50, L"", eos_static_ex);
	bstatic5->SetColor(color_background, UIColor(230, 231, 232, 255));
	bstatic5->SetColor(color_border, UIColor(194, 195, 201, 255));
	bstatic5->AddText(L"测试文本");
	bstatic5->AddText(L"自定义文本🙂", UIColor(112, 132, 255, 255), {}, L"", 17);
	bstatic5->AddText(L"自定义文本2", UIColor(252, 164, 84, 255), {}, L"", 17, ::FontStyle::Italic);


	window->Show();
}