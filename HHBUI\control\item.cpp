﻿#include "pch.h"
#include "item.h"
#include <common/winapi.h>

HHBUI::UIItem::UIItem(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
    InitSubControl(hParent, x, y, width, height, L"form-item", lpszName, dwStyle, dwStyleEx, nID, dwTextFormat);
    m_hBrush = new UIBrush();
}

LRESULT HHBUI::UIItem::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_DESTROY)
	{
        delete m_hBrush;
        return S_OK;
	}
    else if (uMsg == WM_EX_LCLICK)
    {
        if ((m_data.dwFlags & EOF_BMENUITEM) == EOF_BMENUITEM)
        {
            if ((m_data.dwStyle & EMIS_SEPARATOR) == EMIS_SEPARATOR)
                return S_OK;
            else if ((m_data.dwStyle & EMIS_SUBMENU) == EMIS_SUBMENU)
                return S_OK;
            else
            {
                auto pWnd = m_data.pWnd;
                MENUITEMINFOW mii{ 0 };
                mii.cbSize = sizeof(MENUITEMINFOW);
                mii.fMask = MIIM_STATE | MIIM_ID;
                if (GetMenuItemInfoW(pWnd->m_data.hMenuPopup, m_data.lParam, TRUE, &mii))
                {
                    if ((mii.fState & MFS_GRAYED) == 0)
                    {
                        auto pMenuHostWnd = (UIWnd*)pWnd->m_data.pMenuHostWnd;
                        ShowWindow(hWnd, SW_HIDE);
                        PostMessageW(pMenuHostWnd->m_data.hWnd, WM_COMMAND, mii.wID, (size_t)pWnd->m_data.hMenuPopup);
                        menu_s* menu = pWnd->m_data.lpMenuParams;
                        if (menu->pfnCallback)
                            menu->pfnCallback(hWnd, pWnd, m_UIView, mii.wID, WM_EX_MENU, (size_t)pWnd->m_data.hMenuPopup, 0);
                    
                        EndMenu();
                        return S_OK;
                    }
                }
            }
        }
    }
    else if (uMsg == WM_MOUSEHOVER)
    {
        if ((m_data.dwFlags & EOF_BMENUITEM) == EOF_BMENUITEM)
        {
            OnBrothers(WM_MOUSELEAVE, 0, 0, TRUE, TRUE);
        }
        SetFocus();
    }
    else if (uMsg == WM_MOUSELEAVE)
    {
        if ((m_data.dwState & state_hover) == state_hover)
        {
            KillFocus();
            SetState(state_hover | state_down, TRUE);
            Redraw();
        }
    }
    else if (uMsg == WM_SETFOCUS)
    {
        SetState(state_hover, FALSE);
        Redraw();
    }
    else if (uMsg == WM_KILLFOCUS)
    {
        SetState(state_hover | state_down, TRUE);
        Redraw();
    }
	return S_OK;
}

void HHBUI::UIItem::OnPaintProc(ps_context ps)
{
    if ((m_data.dwFlags & EOF_BMENUITEM) == EOF_BMENUITEM)
    {
        HMENU hMenu = (HMENU)m_data.pWnd->m_data.hMenuPopup;
        MENUITEMINFOW mii{ 0 };
        mii.cbSize = sizeof(MENUITEMINFOW);
        mii.fMask = MIIM_STATE | MIIM_FTYPE | MIIM_DATA | MIIM_SUBMENU;
        UINT nID = m_data.lParam;
        RECT rcItem{ 0 };
        RECT rcPadding{ 0 };
        RECT rcSub{ 0 };
        rcItem.right = ps.uWidth;
        rcItem.bottom = ps.uHeight;
        INT atomProp = 0;
        if (GetMenuItemInfoW(hMenu, nID, TRUE, &mii))
        {
            UIColor crColor;
            GetColor(color_text_normal, crColor);
            if ((ps.dwState & state_hover) != 0 && (mii.fState & MFS_GRAYED) == 0)
            {
                GetColor(color_text_hover, crColor);
                if (crColor.empty())
                {
                    GetColor(color_text_normal, crColor);
                    crColor.SetA(128);
                }
            }
            else
            {
                if ((ps.dwState & state_checked) != 0)
                {
                }
                if ((ps.dwState & state_select) != 0)
                {
                }
            }
            if ((mii.fType & MFT_SEPARATOR) != 0)
            {
                rcItem.left = UIEngine::ScaleValue(3);
                rcItem.top = (rcItem.bottom - 2) / 2;
                rcItem.right = rcItem.right - UIEngine::ScaleValue(3);
                rcItem.bottom = rcItem.top + 2;
                m_hBrush->SetColor(UIColor(194, 195, 201, 255));
                ps.hCanvas->DrawLine(m_hBrush, rcItem.left, rcItem.top + 1, rcItem.right, rcItem.top + 1, 1.f);

            }
            else
            {
                BOOL fHover = ((ps.dwState & state_hover) != 0 && (mii.fState & MFS_GRAYED) == 0) || ((mii.fState & MFS_HILITE) != 0 && mii.hSubMenu != 0);
                if (fHover)
                {
                    m_data.dwState |= state_hover;
                }
                if ((mii.fState & MFS_GRAYED) == 0)
                {
                    if ((ps.dwState & state_hover) != 0)
                    {
                        auto pWnd = GetUIWnd();
                        UIColor Colorp = *reinterpret_cast<UIColor*>(pWnd->GetlParam());
                        if (Colorp.empty())
                            Colorp = UIColor(228, 228, 228, 255);
                        m_hBrush->SetColor(Colorp);
                        ps.hCanvas->FillRoundRect(m_hBrush, rcItem.left, rcItem.top + 2, rcItem.right - 2, rcItem.bottom - 2, m_data.radius.left);
                    }
                }
                else
                {
                    crColor.A(128);
                }
                if (mii.hSubMenu != 0)
                {
                    m_hBrush->SetColor(crColor);
                    ps.hCanvas->DrawLine(m_hBrush, ps.uWidth - UIEngine::ScaleValue(18), UIEngine::ScaleValue(11), ps.uWidth - UIEngine::ScaleValue(18) + 8, ps.uHeight / static_cast<float>(2), 1.0f);
                    ps.hCanvas->DrawLine(m_hBrush, ps.uWidth - UIEngine::ScaleValue(18), ps.uHeight - UIEngine::ScaleValue(11), ps.uWidth - UIEngine::ScaleValue(18) + 8, ps.uHeight / static_cast<float>(2), 1.0f);
                }
                if ((mii.fState & MFS_CHECKED) != 0)
                {
                }
                else if ((mii.fState & MFT_RADIOCHECK) != 0)
                {
                }
                rcPadding.left = 0;
                rcPadding.top = 0;
                rcPadding.right = 90;
                rcPadding.bottom = 32;

                rcSub.left = rcItem.left + (m_data.Frame_t.left - (rcPadding.right - rcPadding.left)) / 2;
                rcSub.right = rcSub.left + rcPadding.right - rcPadding.left;
                rcSub.top = rcItem.top + (rcItem.bottom - rcItem.top - (rcPadding.bottom - rcPadding.top)) / 2;
                rcSub.bottom = rcSub.top + rcPadding.bottom - rcPadding.top;
                //jo_UIcanvas_drawcontrolthemeex(ps.hTheme, ps.hCanvas, rcSub.left, rcSub.top, rcSub.right, rcSub.bottom, ATOM_ITEM, atomProp, 0, 0, 0, 0, alpha);
                auto lpwz = GetText();
                if (lpwz)
                {
                    if (mii.dwItemData)
                    {
                        UINT imgWidth, imgHeight;
                        auto tmp = (UIImage*)mii.dwItemData;
                        tmp->GetSize(imgWidth, imgHeight);
                        //imgWidth = UIEngine::fScale(imgWidth);
                        //imgHeight = UIEngine::fScale(imgHeight);
                        ExRectF rect = ExRectF(rcItem.left + 4, (ps.uHeight - imgHeight) / 2, imgWidth, imgHeight, TRUE).Normalize();

                        ps.hCanvas->DrawImagePartRect(tmp, rect.left, rect.top, rect.right, rect.bottom, 0, 0, imgWidth, imgHeight);
                    }
                    auto tmp1 = (LPWSTR)wcschr(lpwz, 9);
                    if (tmp1)
                    {
                        *tmp1 = 0;
                    }
                    ps.hCanvas->DrawTextByColor(ps.hFont, lpwz, ps.dwTextFormat, ps.rcText.left, ps.rcText.top, ps.rcText.right + 3, ps.rcText.bottom, crColor);
                    if (tmp1)
                    {
                        *tmp1 = 9;
                        if (!fHover)
                        {
                            crColor.SetA(128);
                        }
                        ps.hCanvas->DrawTextByColor(ps.hFont, (tmp1 + 1), ps.dwTextFormat | DT_RIGHT, ps.rcText.left, ps.rcText.top, ps.rcText.right, ps.rcText.bottom, crColor);
                    }
                }
            }
        }
    }
}

