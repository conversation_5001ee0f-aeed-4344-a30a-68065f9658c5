﻿#include "pch.h"
#include "timeline.h"

HHBUI::UITimeLine::UITimeLine(UIBase* hParent, INT x, INT y, INT w, INT h, INT nID)
{
	InitSubControl(hParent, x, y, w, h, L"form-timeline", NULL, eos_scroll_v | eos_scroll_amds, eos_ex_focusable, nID);

	p_data.w = UIEngine::ScaleValue(w);
    p_data.h = UIEngine::ScaleValue(h);

	SetFontFromFamily(L"微软雅黑", 12, 0);
	p_data.brGen = new UIBrush(UIColor(179, 179, 181));
	p_data.ftTime = new UIFont(L"微软雅黑", 11, 0);
	p_data.ftTitle = new UIFont(L"微软雅黑", 12, 1);

	SetScrollEnable(SB_VERT, ESB_DISABLE_BOTH);
	SetScrollRadius(TRUE);
}

INT HHBUI::UITimeLine::AddItem(LPCWSTR text, LPCWSTR time, LPCWSTR title, INT index)
{
	item_info item;
	item.text = text;
    item.time = time;
    item.title = title;

	int ind = 0;
	if (index < 0) {
		p_data.list.push_back(item);
		ind = (INT)p_data.list.size() - 1;
	}
	else {
		p_data.list.insert(p_data.list.begin() + index, item);
        ind = index;
	}

	return ind;
}

BOOL HHBUI::UITimeLine::SetItemText(INT index, LPCWSTR text)
{
	if (index >= 0 && index < p_data.list.size()) {
		p_data.list.at(index).text = text;
		return TRUE;
	}
	return FALSE;
}

BOOL HHBUI::UITimeLine::SetItemTime(INT index, LPCWSTR time)
{
	if (index >= 0 && index < p_data.list.size()) {
		p_data.list.at(index).time = time;
		return TRUE;
	}
	return FALSE;
}

BOOL HHBUI::UITimeLine::SetItemTitle(INT index, LPCWSTR title)
{
	if (index >= 0 && index < p_data.list.size()) {
		p_data.list.at(index).title = title;
		return TRUE;
	}
    return FALSE;
}

BOOL HHBUI::UITimeLine::SetItemNodeColor(INT index, UIColor color)
{
	if (index >= 0 && index < p_data.list.size()) {
		p_data.list.at(index).crNode = color;
		return TRUE;
	}
    return FALSE;
}

BOOL HHBUI::UITimeLine::SetItemTextColor(INT index, UIColor color)
{
	if (index >= 0 && index < p_data.list.size()) {
		p_data.list.at(index).crText = color;
		return TRUE;
	}
	return FALSE;
}

BOOL HHBUI::UITimeLine::SetItemTimeColor(INT index, UIColor color)
{
	if (index >= 0 && index < p_data.list.size()) {
		p_data.list.at(index).crTime = color;
		return TRUE;
	}
    return FALSE;
}

BOOL HHBUI::UITimeLine::SetItemTitleColor(INT index, UIColor color)
{
	if (index >= 0 && index < p_data.list.size()) {
		p_data.list.at(index).crTitle = color;
		return TRUE;
	}
	return FALSE;
}

BOOL HHBUI::UITimeLine::DelItem(INT index)
{
	if (index >= 0 && index < p_data.list.size()) {
		p_data.list.erase(p_data.list.begin() + index);
		return TRUE;
	}
	return FALSE;
}

void HHBUI::UITimeLine::ClearItem()
{
	p_data.list.clear();
}


LPCWSTR HHBUI::UITimeLine::GetItemText(INT index)
{
	if (index >= 0 && index < p_data.list.size()) {
		return p_data.list.at(index).text;
	}
	return nullptr;
}

LPCWSTR HHBUI::UITimeLine::GetItemTime(INT index)
{
	if (index >= 0 && index < p_data.list.size()) {
		return p_data.list.at(index).time;
	}
    return nullptr;
}

LPCWSTR HHBUI::UITimeLine::GetItemTitle(INT index)
{
	if (index >= 0 && index < p_data.list.size()) {
		return p_data.list.at(index).title;
	}
    return nullptr;
}

INT HHBUI::UITimeLine::GetItemCount()
{
	return (INT)p_data.list.size();
}


void HHBUI::UITimeLine::SetItemTextFont(LPCWSTR name, INT size, INT Style)
{
	SetFontFromFamily(name, size, Style);
}

void HHBUI::UITimeLine::SetItemTimeFont(LPCWSTR name, INT size, INT Style)
{
	if (p_data.ftTime) delete p_data.ftTime;
	p_data.ftTime = new UIFont(name, size, Style);
}

void HHBUI::UITimeLine::SetItemTitleFont(LPCWSTR name, INT size, INT Style)
{
	if (p_data.ftTitle) delete p_data.ftTitle;
	p_data.ftTitle = new UIFont(name, size, Style);
}


void HHBUI::UITimeLine::SetShowTitle(BOOL fShow)
{
	p_data.fShowT = fShow;
}

void HHBUI::UITimeLine::SetShowTimeTop(BOOL fTop)
{
	p_data.ftTop = fTop;
}

void HHBUI::UITimeLine::SetNodeSize(float size)
{
	p_data.ns = size;
}


void HHBUI::UITimeLine::Update()
{
	calu_data();
	Redraw();
}


void HHBUI::UITimeLine::calu_data()
{
	float ptop = 0.f;
	float spc = UIEngine::ScaleValue(6);
	for (auto& item : p_data.list) {
		item.rc.left = 0;
		item.rc.right = p_data.w;

		item.rc.top = ptop;
		item.npt.x = spc + p_data.ns;

		item.trc.left = p_data.ns * 2 + spc * 2;
		item.trc.right = item.rc.right - spc;

		float tw = 0, th = 0;
		if (p_data.fShowT) {
			UICanvas::CalcTextSize(p_data.ftTitle, item.title, Left, item.rc.right - item.trc.left - spc, 99999, &tw, &th);
			item.tlc.left = item.trc.left;
			item.tlc.right = std::min(item.trc.left + tw, item.rc.right - spc);
			item.tlc.top = item.rc.top + spc;
			item.tlc.bottom = item.tlc.top + th;

			item.npt.y = item.tlc.top + (th / 2.f);
		}

        if (p_data.ftTop) {
			UICanvas::CalcTextSize(p_data.ftTime, item.time, Left, item.rc.right - item.trc.left - spc, UIEngine::ScaleValue(48), &tw, &th);
			if (p_data.fShowT) {
				if (item.rc.right - spc * 2 - item.tlc.right >= tw) {
					item.tic.left = item.tlc.right + spc;
					item.tic.top = item.tlc.bottom - th;
				}
				else {
                    item.tic.left = item.trc.left;
					item.tic.top = item.tlc.bottom + spc;
				}
			}
			else {
				item.tic.left = item.trc.left;
                item.tic.top = item.rc.top + spc;

				item.npt.y = item.tic.top + (th / 2.f);
			}
			item.tic.right = item.tic.left + tw;
			item.tic.bottom = item.tic.top + th;

			UICanvas::CalcTextSize(m_data.hFont, item.text, Left, item.rc.right - item.trc.left - spc, 99999, &tw, &th);
			item.trc.top = item.tic.bottom + spc;
            item.trc.bottom = item.trc.top + th;

			item.rc.bottom = item.trc.bottom + spc;
		}
		else {
			UICanvas::CalcTextSize(m_data.hFont, item.text, Left, item.rc.right - item.trc.left - spc, 99999, &tw, &th);
			if (p_data.fShowT) {
				item.trc.top = item.tlc.bottom + spc;
			}
			else {
                item.trc.top = item.rc.top + spc;

				item.npt.y = item.trc.top + spc + p_data.ns;
			}
			item.trc.bottom = item.trc.top + th;

			UICanvas::CalcTextSize(p_data.ftTime, item.time, Left | SingleLine, item.rc.right - item.trc.left - spc, UIEngine::ScaleValue(36), &tw, &th);
			item.tic.left = item.trc.left;
			item.tic.right = item.trc.left + tw;
			item.tic.top = item.trc.bottom + spc;
            item.tic.bottom = item.tic.top + th;

			item.rc.bottom = item.tic.bottom + spc;
		}
		ptop = item.rc.bottom;
	}

	auto item = p_data.list.back();
	if (item.rc.bottom > p_data.h) {
		SetScrollInfo(FALSE, SB_VERT, 0, item.rc.bottom - p_data.h, p_data.h, 0, true);
		SetScrollShow(FALSE, TRUE);
	}
	else {
		SetScrollShow(FALSE, FALSE);
		SetScrollInfo(FALSE, SB_VERT, 0, 0, 0, 0);
	}
}


LRESULT HHBUI::UITimeLine::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_VSCROLL) {
		p_data.vh = PostScrollMsg(uMsg, wParam, lParam, 20);
		Redraw();
	}
	else if (uMsg == WM_DESTROY) {
		if(p_data.brGen) delete p_data.brGen;
        if(p_data.ftTime) delete p_data.ftTime;
		if(p_data.ftTitle) delete p_data.ftTitle;
	}
	return S_OK;
}

void HHBUI::UITimeLine::OnPaintProc(ps_context ps)
{
	BeginPaint(ps);

	if (p_data.list.size() >= 2) {
		p_data.brGen->SetColor(UIColor(179, 179, 181));
		ps.hCanvas->DrawLine(p_data.brGen, p_data.list.front().npt.x, p_data.list.front().npt.y - p_data.vh, p_data.list.back().npt.x, p_data.list.back().npt.y - p_data.vh, ps.dpi);
	}

	for (auto& item : p_data.list) {
		p_data.brGen->SetColor(item.crNode);
		ps.hCanvas->DrawPoint(p_data.brGen, item.npt.x, item.npt.y - p_data.vh, p_data.ns * 2, true);

        if (p_data.fShowT) {
			ps.hCanvas->DrawTextByColor(p_data.ftTitle, item.title, Left | EndEllipsis,
				item.tlc.left, item.tlc.top - p_data.vh, item.tlc.right, item.tlc.bottom - p_data.vh, item.crTitle);
		}

		ps.hCanvas->DrawTextByColor(p_data.ftTime, item.time, Left | EndEllipsis,
			item.tic.left, item.tic.top - p_data.vh, item.tic.right, item.tic.bottom - p_data.vh, item.crTime);

		ps.hCanvas->DrawTextByColor(ps.hFont, item.text, Left | EndEllipsis,
			item.trc.left, item.trc.top - p_data.vh, item.trc.right, item.trc.bottom - p_data.vh, item.crText);

	}

	EndPaint();
}
