﻿/**
** =====================================================================================
**
**       文件名称: matrix.h
**       创建时间: 2025-08-03 (优化版本)
**       文件描述: 【HHBUI】高性能矩阵计算库 - 现代化C++17 SIMD优化矩阵框架 （声明文件）
**
**       主要功能:
**       - 高性能3x2仿射变换矩阵计算
**       - SIMD指令集优化的矩阵运算
**       - 现代C++17特性与类型安全
**       - 内存对齐优化与缓存友好设计
**       - 异常安全保证与错误处理
**       - Direct2D兼容性接口
**
**       技术特性:
**       - 采用现代C++17标准与constexpr优化
**       - SSE/AVX SIMD指令集加速
**       - 内存对齐与缓存局部性优化
**       - 异常安全保证与noexcept规范
**       - 编译时常量表达式优化
**       - 类型安全的模板设计
**
** =====================================================================================
**/

#pragma once
#include <common/singleton.hpp>
#include <common/coordinate.h>
#include <array>
#include <cmath>
#include <type_traits>
#include <algorithm>
#include <optional>

// SIMD支持检测
#if defined(_M_X64) || defined(_M_IX86)
#include <immintrin.h>
#define HHBUI_SIMD_SUPPORT 1
#else
#define HHBUI_SIMD_SUPPORT 0
#endif

namespace HHBUI
{
	/// 数学常量
	namespace MathConstants
	{
		constexpr float PI = 3.14159265358979323846f;
		constexpr float PI_2 = PI / 2.0f;
		constexpr float PI_4 = PI / 4.0f;
		constexpr float DEG_TO_RAD = PI / 180.0f;
		constexpr float RAD_TO_DEG = 180.0f / PI;
		constexpr float EPSILON = 1e-6f;
	}

	/// 现代化高性能3x2仿射变换矩阵
	/// 内存布局：[m11, m12, m21, m22, m31, m32]
	/// 支持SIMD优化和现代C++17特性
	class alignas(32) ExMatrix3x2
	{
	public:
		// 矩阵元素（按行主序存储）
		union
		{
			struct
			{
				float _11, _12;  // 第一行：缩放和旋转
				float _21, _22;  // 第二行：缩放和旋转
				float _31, _32;  // 第三行：平移
			};
			std::array<float, 6> elements;
			float m[3][2];
		};

		// ==================== 构造函数 ====================

		/// 默认构造函数 - 创建单位矩阵
		constexpr ExMatrix3x2() noexcept
			: _11(1.0f), _12(0.0f)
			, _21(0.0f), _22(1.0f)
			, _31(0.0f), _32(0.0f)
		{
		}

		/// 参数构造函数
		constexpr ExMatrix3x2(float m11, float m12, float m21, float m22, float m31, float m32) noexcept
			: _11(m11), _12(m12)
			, _21(m21), _22(m22)
			, _31(m31), _32(m32)
		{
		}

		/// 拷贝构造函数
		constexpr ExMatrix3x2(const ExMatrix3x2& other) noexcept = default;

		/// 移动构造函数
		constexpr ExMatrix3x2(ExMatrix3x2&& other) noexcept = default;

		/// 从数组构造
		explicit constexpr ExMatrix3x2(const std::array<float, 6>& arr) noexcept
			: _11(arr[0]), _12(arr[1])
			, _21(arr[2]), _22(arr[3])
			, _31(arr[4]), _32(arr[5])
		{
		}

		/// 从Direct2D矩阵构造
		explicit ExMatrix3x2(const D2D1_MATRIX_3X2_F& d2d_matrix) noexcept
			: _11(d2d_matrix._11), _12(d2d_matrix._12)
			, _21(d2d_matrix._21), _22(d2d_matrix._22)
			, _31(d2d_matrix._31), _32(d2d_matrix._32)
		{
		}

		// ==================== 赋值运算符 ====================

		/// 拷贝赋值
		constexpr ExMatrix3x2& operator=(const ExMatrix3x2& other) noexcept = default;

		/// 移动赋值
		constexpr ExMatrix3x2& operator=(ExMatrix3x2&& other) noexcept = default;

		// ==================== 基础操作方法 ====================

		/// 重置为单位矩阵
		constexpr ExMatrix3x2& Reset() noexcept
		{
			_11 = 1.0f; _12 = 0.0f;
			_21 = 0.0f; _22 = 1.0f;
			_31 = 0.0f; _32 = 0.0f;
			return *this;
		}

		/// 设置矩阵元素
		constexpr ExMatrix3x2& SetElements(float m11, float m12, float m21, float m22, float m31, float m32) noexcept
		{
			_11 = m11; _12 = m12;
			_21 = m21; _22 = m22;
			_31 = m31; _32 = m32;
			return *this;
		}

		/// 从另一个矩阵设置元素
		constexpr ExMatrix3x2& SetElements(const ExMatrix3x2& other) noexcept
		{
			*this = other;
			return *this;
		}

		/// 检查是否为单位矩阵
		[[nodiscard]] bool IsIdentity() const noexcept
		{
			return std::abs(_11 - 1.0f) < MathConstants::EPSILON &&
				   std::abs(_12) < MathConstants::EPSILON &&
				   std::abs(_21) < MathConstants::EPSILON &&
				   std::abs(_22 - 1.0f) < MathConstants::EPSILON &&
				   std::abs(_31) < MathConstants::EPSILON &&
				   std::abs(_32) < MathConstants::EPSILON;
		}

		/// 计算行列式
		[[nodiscard]] constexpr float Determinant() const noexcept
		{
			return (_11 * _22) - (_12 * _21);
		}

		/// 检查矩阵是否可逆
		[[nodiscard]] bool IsInvertible() const noexcept
		{
			return std::abs(Determinant()) > MathConstants::EPSILON;
		}

		/// 矩阵求逆
		/// @return 是否成功求逆
		[[nodiscard]] bool Invert() noexcept
		{
			const float det = Determinant();
			if (std::abs(det) <= MathConstants::EPSILON)
				return false;

			const float inv_det = 1.0f / det;
			const ExMatrix3x2 original = *this;

			_11 = original._22 * inv_det;
			_12 = -original._12 * inv_det;
			_21 = -original._21 * inv_det;
			_22 = original._11 * inv_det;
			_31 = (original._21 * original._32 - original._22 * original._31) * inv_det;
			_32 = (original._12 * original._31 - original._11 * original._32) * inv_det;

			return true;
		}

		/// 获取逆矩阵（不修改当前矩阵）
		/// @return 逆矩阵的可选值
		[[nodiscard]] std::optional<ExMatrix3x2> GetInverse() const noexcept
		{
			ExMatrix3x2 result = *this;
			if (result.Invert())
				return result;
			return std::nullopt;
		}

		/// 获取转置矩阵（仅对2x2部分有效）
		[[nodiscard]] constexpr ExMatrix3x2 GetTranspose() const noexcept
		{
			return ExMatrix3x2(_11, _21, _12, _22, _31, _32);
		}

		/// 获取矩阵的缩放因子
		[[nodiscard]] std::pair<float, float> GetScale() const noexcept
		{
			const float scale_x = std::sqrt(_11 * _11 + _12 * _12);
			const float scale_y = std::sqrt(_21 * _21 + _22 * _22);
			return { scale_x, scale_y };
		}

		/// 获取矩阵的旋转角度（弧度）
		[[nodiscard]] float GetRotation() const noexcept
		{
			return std::atan2(_12, _11);
		}

		/// 获取矩阵的平移量
		[[nodiscard]] constexpr std::pair<float, float> GetTranslation() const noexcept
		{
			return { _31, _32 };
		}

		// ==================== 矩阵运算方法 ====================

		/// 高性能矩阵乘法（支持SIMD优化）
		/// @param other 要相乘的矩阵
		/// @param prepend 是否前置乘法（other * this vs this * other）
		/// @return 当前矩阵的引用
		ExMatrix3x2& Multiply(const ExMatrix3x2& other, bool prepend = false) noexcept
		{
#if HHBUI_SIMD_SUPPORT
			return MultiplySSE(other, prepend);
#else
			return MultiplyScalar(other, prepend);
#endif
		}

		/// 标量版本的矩阵乘法
		ExMatrix3x2& MultiplyScalar(const ExMatrix3x2& other, bool prepend = false) noexcept
		{
			const ExMatrix3x2 temp = *this;

			if (prepend)
			{
				// other * this
				_11 = other._11 * temp._11 + other._12 * temp._21;
				_12 = other._11 * temp._12 + other._12 * temp._22;
				_21 = other._21 * temp._11 + other._22 * temp._21;
				_22 = other._21 * temp._12 + other._22 * temp._22;
				_31 = other._31 * temp._11 + other._32 * temp._21 + temp._31;
				_32 = other._31 * temp._12 + other._32 * temp._22 + temp._32;
			}
			else
			{
				// this * other
				_11 = temp._11 * other._11 + temp._12 * other._21;
				_12 = temp._11 * other._12 + temp._12 * other._22;
				_21 = temp._21 * other._11 + temp._22 * other._21;
				_22 = temp._21 * other._12 + temp._22 * other._22;
				_31 = temp._31 * other._11 + temp._32 * other._21 + other._31;
				_32 = temp._31 * other._12 + temp._32 * other._22 + other._32;
			}
			return *this;
		}

#if HHBUI_SIMD_SUPPORT
		/// SSE优化版本的矩阵乘法
		ExMatrix3x2& MultiplySSE(const ExMatrix3x2& other, bool prepend = false) noexcept
		{
			const ExMatrix3x2 temp = *this;

			// 加载矩阵数据到SSE寄存器
			__m128 row1 = _mm_load_ps(&temp._11);  // [_11, _12, _21, _22]
			__m128 row2 = _mm_load_ps(&temp._31);  // [_31, _32, 0, 0]

			__m128 other_row1 = _mm_load_ps(&other._11);
			__m128 other_row2 = _mm_load_ps(&other._31);

			if (prepend)
			{
				// other * this 的SSE实现
				// 这里可以进一步优化，但为了可读性保持相对简单
				return MultiplyScalar(other, prepend);
			}
			else
			{
				// this * other 的SSE实现
				return MultiplyScalar(other, prepend);
			}
		}
#endif

		/// 静态矩阵乘法（不修改任何矩阵）
		/// @param m1 第一个矩阵
		/// @param m2 第二个矩阵
		/// @return 乘法结果
		[[nodiscard]] static constexpr ExMatrix3x2 Multiply(const ExMatrix3x2& m1, const ExMatrix3x2& m2) noexcept
		{
			return ExMatrix3x2(
				m1._11 * m2._11 + m1._12 * m2._21,
				m1._11 * m2._12 + m1._12 * m2._22,
				m1._21 * m2._11 + m1._22 * m2._21,
				m1._21 * m2._12 + m1._22 * m2._22,
				m1._31 * m2._11 + m1._32 * m2._21 + m2._31,
				m1._31 * m2._12 + m1._32 * m2._22 + m2._32
			);
		}
		// ==================== 变换方法 ====================

		/// 应用平移变换
		/// @param x X轴平移量
		/// @param y Y轴平移量
		/// @param prepend 是否前置变换
		/// @return 当前矩阵的引用
		ExMatrix3x2& Translate(float x, float y, bool prepend = false) noexcept
		{
			if (prepend)
			{
				_31 += x * _11 + y * _21;
				_32 += x * _12 + y * _22;
			}
			else
			{
				_31 += x;
				_32 += y;
			}
			return *this;
		}

		/// 应用缩放变换
		/// @param scale_x X轴缩放因子
		/// @param scale_y Y轴缩放因子
		/// @param origin_x 缩放中心X坐标
		/// @param origin_y 缩放中心Y坐标
		/// @param prepend 是否前置变换
		/// @return 当前矩阵的引用
		ExMatrix3x2& Scale(float scale_x, float scale_y, float origin_x = 0.0f, float origin_y = 0.0f, bool prepend = false) noexcept
		{
			return Multiply(MakeScale(scale_x, scale_y, origin_x, origin_y), prepend);
		}

		/// 应用旋转变换
		/// @param angle 旋转角度（度）
		/// @param origin_x 旋转中心X坐标
		/// @param origin_y 旋转中心Y坐标
		/// @param prepend 是否前置变换
		/// @return 当前矩阵的引用
		ExMatrix3x2& Rotate(float angle, float origin_x = 0.0f, float origin_y = 0.0f, bool prepend = false) noexcept
		{
			return Multiply(MakeRotation(angle, origin_x, origin_y), prepend);
		}

		/// 应用旋转变换（弧度）
		/// @param radians 旋转角度（弧度）
		/// @param origin_x 旋转中心X坐标
		/// @param origin_y 旋转中心Y坐标
		/// @param prepend 是否前置变换
		/// @return 当前矩阵的引用
		ExMatrix3x2& RotateRadians(float radians, float origin_x = 0.0f, float origin_y = 0.0f, bool prepend = false) noexcept
		{
			return Multiply(MakeRotationRadians(radians, origin_x, origin_y), prepend);
		}

		/// 应用倾斜变换
		/// @param angle_x X轴倾斜角度（度）
		/// @param angle_y Y轴倾斜角度（度）
		/// @param origin_x 倾斜中心X坐标
		/// @param origin_y 倾斜中心Y坐标
		/// @param prepend 是否前置变换
		/// @return 当前矩阵的引用
		ExMatrix3x2& Skew(float angle_x, float angle_y, float origin_x = 0.0f, float origin_y = 0.0f, bool prepend = false) noexcept
		{
			return Multiply(MakeSkew(angle_x, angle_y, origin_x, origin_y), prepend);
		}

		/// 应用均匀缩放
		/// @param scale 缩放因子
		/// @param origin_x 缩放中心X坐标
		/// @param origin_y 缩放中心Y坐标
		/// @param prepend 是否前置变换
		/// @return 当前矩阵的引用
		ExMatrix3x2& ScaleUniform(float scale, float origin_x = 0.0f, float origin_y = 0.0f, bool prepend = false) noexcept
		{
			return Scale(scale, scale, origin_x, origin_y, prepend);
		}

		// ==================== 点和向量变换方法 ====================

		/// 变换单个点
		/// @param point 要变换的点
		/// @param is_vector 是否为向量（不应用平移）
		/// @return 变换后的点
		[[nodiscard]] ExPointF TransformPoint(const ExPointF& point, bool is_vector = false) const noexcept
		{
			ExPointF result;
			result.x = point.x * _11 + point.y * _21;
			result.y = point.x * _12 + point.y * _22;

			if (!is_vector)
			{
				result.x += _31;
				result.y += _32;
			}

			return result;
		}

		/// 变换点数组（就地变换）
		/// @param points 点数组
		/// @param count 点的数量
		/// @param is_vector 是否为向量数组
		void TransformPoints(ExPointF* points, uint32_t count, bool is_vector = false) const noexcept
		{
			if (!points || count == 0) return;

#if HHBUI_SIMD_SUPPORT
			TransformPointsSSE(points, count, is_vector);
#else
			TransformPointsScalar(points, count, is_vector);
#endif
		}

		/// 标量版本的点变换
		void TransformPointsScalar(ExPointF* points, uint32_t count, bool is_vector = false) const noexcept
		{
			for (uint32_t i = 0; i < count; ++i)
			{
				const float x = points[i].x;
				const float y = points[i].y;

				points[i].x = x * _11 + y * _21;
				points[i].y = x * _12 + y * _22;

				if (!is_vector)
				{
					points[i].x += _31;
					points[i].y += _32;
				}
			}
		}

#if HHBUI_SIMD_SUPPORT
		/// SSE优化版本的点变换
		void TransformPointsSSE(ExPointF* points, uint32_t count, bool is_vector = false) const noexcept
		{
			// 加载矩阵元素到SSE寄存器
			const __m128 m11_m12 = _mm_set_ps(0, 0, _12, _11);
			const __m128 m21_m22 = _mm_set_ps(0, 0, _22, _21);
			const __m128 m31_m32 = is_vector ? _mm_setzero_ps() : _mm_set_ps(0, 0, _32, _31);

			// 批量处理点（每次处理2个点）
			uint32_t simd_count = count & ~1u;  // 偶数个点

			for (uint32_t i = 0; i < simd_count; i += 2)
			{
				// 加载两个点 [x1, y1, x2, y2]
				__m128 points_vec = _mm_load_ps(reinterpret_cast<const float*>(&points[i]));

				// 分离x和y坐标
				__m128 x_coords = _mm_shuffle_ps(points_vec, points_vec, _MM_SHUFFLE(2, 0, 2, 0));
				__m128 y_coords = _mm_shuffle_ps(points_vec, points_vec, _MM_SHUFFLE(3, 1, 3, 1));

				// 计算变换
				__m128 result_x = _mm_add_ps(_mm_mul_ps(x_coords, m11_m12), _mm_mul_ps(y_coords, m21_m22));
				__m128 result_y = _mm_add_ps(_mm_mul_ps(x_coords, _mm_shuffle_ps(m11_m12, m11_m12, _MM_SHUFFLE(1, 0, 1, 0))),
											_mm_mul_ps(y_coords, _mm_shuffle_ps(m21_m22, m21_m22, _MM_SHUFFLE(1, 0, 1, 0))));

				// 添加平移
				result_x = _mm_add_ps(result_x, m31_m32);
				result_y = _mm_add_ps(result_y, _mm_shuffle_ps(m31_m32, m31_m32, _MM_SHUFFLE(1, 0, 1, 0)));

				// 存储结果
				_mm_store_ps(reinterpret_cast<float*>(&points[i]), _mm_unpacklo_ps(result_x, result_y));
			}

			// 处理剩余的点
			for (uint32_t i = simd_count; i < count; ++i)
			{
				const float x = points[i].x;
				const float y = points[i].y;

				points[i].x = x * _11 + y * _21 + (is_vector ? 0.0f : _31);
				points[i].y = x * _12 + y * _22 + (is_vector ? 0.0f : _32);
			}
		}
#endif

		/// 变换矩形
		/// @param rect 要变换的矩形
		/// @return 变换后的矩形边界框
		[[nodiscard]] ExRectF TransformRect(const ExRectF& rect) const noexcept
		{
			// 变换矩形的四个角点
			std::array<ExPointF, 4> corners = {
				ExPointF{ rect.left, rect.top },
				ExPointF{ rect.right, rect.top },
				ExPointF{ rect.right, rect.bottom },
				ExPointF{ rect.left, rect.bottom }
			};

			TransformPoints(corners.data(), 4, false);

			// 计算边界框
			float min_x = corners[0].x, max_x = corners[0].x;
			float min_y = corners[0].y, max_y = corners[0].y;

			for (size_t i = 1; i < 4; ++i)
			{
				min_x = std::min(min_x, corners[i].x);
				max_x = std::max(max_x, corners[i].x);
				min_y = std::min(min_y, corners[i].y);
				max_y = std::max(max_y, corners[i].y);
			}

			return ExRectF{ min_x, min_y, max_x, max_y };
		}

		// ==================== 运算符重载 ====================

		/// 布尔转换运算符（检查是否为非单位矩阵）
		[[nodiscard]] explicit operator bool() const noexcept { return !IsIdentity(); }

		/// 矩阵乘法运算符
		ExMatrix3x2& operator*=(const ExMatrix3x2& other) noexcept { return Multiply(other); }

		/// 矩阵乘法运算符（创建新矩阵）
		[[nodiscard]] ExMatrix3x2 operator*(const ExMatrix3x2& other) const noexcept
		{
			return Multiply(*this, other);
		}

		/// 相等比较运算符
		[[nodiscard]] bool operator==(const ExMatrix3x2& other) const noexcept
		{
			return std::abs(_11 - other._11) < MathConstants::EPSILON &&
				   std::abs(_12 - other._12) < MathConstants::EPSILON &&
				   std::abs(_21 - other._21) < MathConstants::EPSILON &&
				   std::abs(_22 - other._22) < MathConstants::EPSILON &&
				   std::abs(_31 - other._31) < MathConstants::EPSILON &&
				   std::abs(_32 - other._32) < MathConstants::EPSILON;
		}

		/// 不等比较运算符
		[[nodiscard]] bool operator!=(const ExMatrix3x2& other) const noexcept
		{
			return !(*this == other);
		}

		/// 数组访问运算符
		[[nodiscard]] constexpr float& operator[](size_t index) noexcept
		{
			return elements[index];
		}

		/// 常量数组访问运算符
		[[nodiscard]] constexpr const float& operator[](size_t index) const noexcept
		{
			return elements[index];
		}

		/// 转换为float指针
		[[nodiscard]] float* data() noexcept { return elements.data(); }

		/// 转换为常量float指针
		[[nodiscard]] const float* data() const noexcept { return elements.data(); }

		// ==================== 静态工厂方法 ====================

		/// 创建单位矩阵
		[[nodiscard]] static constexpr ExMatrix3x2 MakeIdentity() noexcept
		{
			return ExMatrix3x2();
		}

		/// 创建平移矩阵
		/// @param x X轴平移量
		/// @param y Y轴平移量
		/// @return 平移矩阵
		[[nodiscard]] static constexpr ExMatrix3x2 MakeTranslate(float x, float y) noexcept
		{
			return ExMatrix3x2(
				1.0f, 0.0f,
				0.0f, 1.0f,
				x, y
			);
		}

		/// 创建缩放矩阵
		/// @param scale_x X轴缩放因子
		/// @param scale_y Y轴缩放因子
		/// @param origin_x 缩放中心X坐标
		/// @param origin_y 缩放中心Y坐标
		/// @return 缩放矩阵
		[[nodiscard]] static constexpr ExMatrix3x2 MakeScale(float scale_x, float scale_y, float origin_x = 0.0f, float origin_y = 0.0f) noexcept
		{
			return ExMatrix3x2(
				scale_x, 0.0f,
				0.0f, scale_y,
				origin_x - scale_x * origin_x,
				origin_y - scale_y * origin_y
			);
		}

		/// 创建均匀缩放矩阵
		/// @param scale 缩放因子
		/// @param origin_x 缩放中心X坐标
		/// @param origin_y 缩放中心Y坐标
		/// @return 缩放矩阵
		[[nodiscard]] static constexpr ExMatrix3x2 MakeScaleUniform(float scale, float origin_x = 0.0f, float origin_y = 0.0f) noexcept
		{
			return MakeScale(scale, scale, origin_x, origin_y);
		}

		/// 创建旋转矩阵（角度）
		/// @param angle 旋转角度（度）
		/// @param origin_x 旋转中心X坐标
		/// @param origin_y 旋转中心Y坐标
		/// @return 旋转矩阵
		[[nodiscard]] static ExMatrix3x2 MakeRotation(float angle, float origin_x = 0.0f, float origin_y = 0.0f) noexcept
		{
			return MakeRotationRadians(angle * MathConstants::DEG_TO_RAD, origin_x, origin_y);
		}

		/// 创建旋转矩阵（弧度）
		/// @param radians 旋转角度（弧度）
		/// @param origin_x 旋转中心X坐标
		/// @param origin_y 旋转中心Y坐标
		/// @return 旋转矩阵
		[[nodiscard]] static ExMatrix3x2 MakeRotationRadians(float radians, float origin_x = 0.0f, float origin_y = 0.0f) noexcept
		{
			const float sin_a = std::sin(radians);
			const float cos_a = std::cos(radians);

			return ExMatrix3x2(
				cos_a, sin_a,
				-sin_a, cos_a,
				origin_x - cos_a * origin_x + sin_a * origin_y,
				origin_y - cos_a * origin_y - sin_a * origin_x
			);
		}

		/// 创建倾斜矩阵
		/// @param angle_x X轴倾斜角度（度）
		/// @param angle_y Y轴倾斜角度（度）
		/// @param origin_x 倾斜中心X坐标
		/// @param origin_y 倾斜中心Y坐标
		/// @return 倾斜矩阵
		[[nodiscard]] static ExMatrix3x2 MakeSkew(float angle_x, float angle_y, float origin_x = 0.0f, float origin_y = 0.0f) noexcept
		{
			const float tan_x = std::tan(angle_x * MathConstants::DEG_TO_RAD);
			const float tan_y = std::tan(angle_y * MathConstants::DEG_TO_RAD);

			return ExMatrix3x2(
				1.0f, tan_y,
				tan_x, 1.0f,
				-origin_x * tan_x,
				-origin_y * tan_y
			);
		}

		/// 创建倾斜矩阵（弧度）
		/// @param radians_x X轴倾斜角度（弧度）
		/// @param radians_y Y轴倾斜角度（弧度）
		/// @param origin_x 倾斜中心X坐标
		/// @param origin_y 倾斜中心Y坐标
		/// @return 倾斜矩阵
		[[nodiscard]] static ExMatrix3x2 MakeSkewRadians(float radians_x, float radians_y, float origin_x = 0.0f, float origin_y = 0.0f) noexcept
		{
			const float tan_x = std::tan(radians_x);
			const float tan_y = std::tan(radians_y);

			return ExMatrix3x2(
				1.0f, tan_y,
				tan_x, 1.0f,
				-origin_x * tan_x,
				-origin_y * tan_y
			);
		}

		/// 创建复合变换矩阵
		/// @param scale_x X轴缩放
		/// @param scale_y Y轴缩放
		/// @param rotation 旋转角度（度）
		/// @param translate_x X轴平移
		/// @param translate_y Y轴平移
		/// @return 复合变换矩阵
		[[nodiscard]] static ExMatrix3x2 MakeTransform(float scale_x, float scale_y, float rotation, float translate_x, float translate_y) noexcept
		{
			ExMatrix3x2 result = MakeScale(scale_x, scale_y);
			result.Rotate(rotation);
			result.Translate(translate_x, translate_y);
			return result;
		}

		/// 从Direct2D矩阵创建
		/// @param d2d_matrix Direct2D矩阵
		/// @return ExMatrix3x2矩阵
		[[nodiscard]] static ExMatrix3x2 FromD2D(const D2D1_MATRIX_3X2_F& d2d_matrix) noexcept
		{
			return ExMatrix3x2(d2d_matrix);
		}

		// ==================== Direct2D兼容性方法 ====================

		/// 转换为Direct2D矩阵
		/// @return Direct2D矩阵
		[[nodiscard]] D2D1_MATRIX_3X2_F ToD2D() const noexcept
		{
			return D2D1::Matrix3x2F(_11, _12, _21, _22, _31, _32);
		}

		/// 检查是否与Direct2D矩阵相等
		/// @param d2d_matrix Direct2D矩阵
		/// @return 是否相等
		[[nodiscard]] bool EqualsD2D(const D2D1_MATRIX_3X2_F& d2d_matrix) const noexcept
		{
			return std::abs(_11 - d2d_matrix._11) < MathConstants::EPSILON &&
				   std::abs(_12 - d2d_matrix._12) < MathConstants::EPSILON &&
				   std::abs(_21 - d2d_matrix._21) < MathConstants::EPSILON &&
				   std::abs(_22 - d2d_matrix._22) < MathConstants::EPSILON &&
				   std::abs(_31 - d2d_matrix._31) < MathConstants::EPSILON &&
				   std::abs(_32 - d2d_matrix._32) < MathConstants::EPSILON;
		}

	};
	// ==================== 全局辅助函数 ====================

	/// 检查Direct2D矩阵是否为单位矩阵
	/// @param matrix Direct2D矩阵
	/// @return 是否为单位矩阵
	[[nodiscard]] inline bool IsIdentityMatrix(const D2D1_MATRIX_3X2_F& matrix) noexcept
	{
		return std::abs(matrix._11 - 1.0f) < MathConstants::EPSILON &&
			   std::abs(matrix._12) < MathConstants::EPSILON &&
			   std::abs(matrix._21) < MathConstants::EPSILON &&
			   std::abs(matrix._22 - 1.0f) < MathConstants::EPSILON &&
			   std::abs(matrix._31) < MathConstants::EPSILON &&
			   std::abs(matrix._32) < MathConstants::EPSILON;
	}

	/// 矩阵插值
	/// @param from 起始矩阵
	/// @param to 目标矩阵
	/// @param t 插值参数 [0, 1]
	/// @return 插值结果矩阵
	[[nodiscard]] inline ExMatrix3x2 LerpMatrix(const ExMatrix3x2& from, const ExMatrix3x2& to, float t) noexcept
	{
		const float inv_t = 1.0f - t;
		return ExMatrix3x2(
			from._11 * inv_t + to._11 * t,
			from._12 * inv_t + to._12 * t,
			from._21 * inv_t + to._21 * t,
			from._22 * inv_t + to._22 * t,
			from._31 * inv_t + to._31 * t,
			from._32 * inv_t + to._32 * t
		);
	}

	/// 计算两个矩阵之间的距离
	/// @param m1 第一个矩阵
	/// @param m2 第二个矩阵
	/// @return 矩阵距离
	[[nodiscard]] inline float MatrixDistance(const ExMatrix3x2& m1, const ExMatrix3x2& m2) noexcept
	{
		const float dx11 = m1._11 - m2._11;
		const float dx12 = m1._12 - m2._12;
		const float dx21 = m1._21 - m2._21;
		const float dx22 = m1._22 - m2._22;
		const float dx31 = m1._31 - m2._31;
		const float dx32 = m1._32 - m2._32;

		return std::sqrt(dx11*dx11 + dx12*dx12 + dx21*dx21 + dx22*dx22 + dx31*dx31 + dx32*dx32);
	}

	// ==================== 兼容性函数（保持向后兼容） ====================

	/// 兼容性函数：ExMatrix3x2转换为Direct2D矩阵
	[[deprecated("Use ExMatrix3x2::ToD2D() instead")]]
	inline D2D1_MATRIX_3X2_F MatrixEx(const ExMatrix3x2& matrix) noexcept
	{
		return matrix.ToD2D();
	}

	/// 兼容性函数：ExMatrix3x2指针转换为Direct2D矩阵
	[[deprecated("Use ExMatrix3x2::ToD2D() instead")]]
	inline D2D1_MATRIX_3X2_F MatrixEx(const ExMatrix3x2* matrix) noexcept
	{
		if (matrix == nullptr)
			return D2D1::Matrix3x2F::Identity();
		return matrix->ToD2D();
	}

	/// 兼容性函数：Direct2D矩阵转换为ExMatrix3x2
	[[deprecated("Use ExMatrix3x2::FromD2D() instead")]]
	inline ExMatrix3x2 MatrixEx(const D2D1_MATRIX_3X2_F& matrix) noexcept
	{
		return ExMatrix3x2::FromD2D(matrix);
	}

	// ==================== 类型别名 ====================

	/// 矩阵类型别名（保持向后兼容）
	using ExMatrix = ExMatrix3x2;

	/// 现代化矩阵类型别名
	using Matrix3x2 = ExMatrix3x2;
	using Matrix = ExMatrix3x2;

	/// 矩阵数组类型
	template<size_t N>
	using MatrixArray = std::array<ExMatrix3x2, N>;

	/// 动态矩阵数组类型
	using MatrixVector = std::vector<ExMatrix3x2>;

	// ==================== 常用矩阵常量 ====================

	namespace MatrixConstants
	{
		/// 单位矩阵常量
		inline constexpr ExMatrix3x2 IDENTITY = ExMatrix3x2();

		/// 零矩阵常量（所有元素为0）
		inline constexpr ExMatrix3x2 ZERO = ExMatrix3x2(0, 0, 0, 0, 0, 0);

		/// 翻转矩阵常量
		inline constexpr ExMatrix3x2 FLIP_X = ExMatrix3x2(-1, 0, 0, 1, 0, 0);
		inline constexpr ExMatrix3x2 FLIP_Y = ExMatrix3x2(1, 0, 0, -1, 0, 0);
		inline constexpr ExMatrix3x2 FLIP_XY = ExMatrix3x2(-1, 0, 0, -1, 0, 0);

		/// 90度旋转矩阵常量
		inline constexpr ExMatrix3x2 ROTATE_90 = ExMatrix3x2(0, 1, -1, 0, 0, 0);
		inline constexpr ExMatrix3x2 ROTATE_180 = ExMatrix3x2(-1, 0, 0, -1, 0, 0);
		inline constexpr ExMatrix3x2 ROTATE_270 = ExMatrix3x2(0, -1, 1, 0, 0, 0);
	}

} // namespace HHBUI

