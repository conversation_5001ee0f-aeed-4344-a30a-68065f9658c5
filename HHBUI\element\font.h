﻿#pragma once
namespace HHBUI
{
	/// 字体风格枚举（兼容性版本）
	enum class FontStyleLegacy : uint32_t
	{
	     	Normal = 0x0000,			///< 字体风格：正常
			Bold = 0x0001,				///< 字体风格：粗体
			Italic = 0x0002,			///< 字体风格：斜体
			UnderLine = 0x0004,			///< 字体风格：下划线
			StrikeOut = 0x0008,			///< 字体风格：删除线
			CustomWeight = 0x0100,		///< 字体风格：自定义粗细 <此时style高16位表示粗细,取值一般在0-1000,可参考FW_开头的常量>
	};

	// 为了向后兼容，提供结构体级别的常量（避免与engine.h中的FontStyle冲突）
	struct FontStyleConstants
	{
		static constexpr uint32_t Normal = static_cast<uint32_t>(FontStyleLegacy::Normal);
		static constexpr uint32_t Bold = static_cast<uint32_t>(FontStyleLegacy::Bold);
		static constexpr uint32_t Italic = static_cast<uint32_t>(FontStyleLegacy::Italic);
		static constexpr uint32_t UnderLine = static_cast<uint32_t>(FontStyleLegacy::UnderLine);
		static constexpr uint32_t StrikeOut = static_cast<uint32_t>(FontStyleLegacy::StrikeOut);
		static constexpr uint32_t CustomWeight = static_cast<uint32_t>(FontStyleLegacy::CustomWeight);
	};

	// 为了向后兼容，提供全局常量
	namespace FontStyleCompat
	{
		constexpr uint32_t Normal = FontStyleConstants::Normal;
		constexpr uint32_t Bold = FontStyleConstants::Bold;
		constexpr uint32_t Italic = FontStyleConstants::Italic;
		constexpr uint32_t UnderLine = FontStyleConstants::UnderLine;
		constexpr uint32_t StrikeOut = FontStyleConstants::StrikeOut;
		constexpr uint32_t CustomWeight = FontStyleConstants::CustomWeight;
	}

	// 在HHBUI命名空间内部提供简单常量（用于默认参数）
	constexpr uint32_t FONT_STYLE_NORMAL = FontStyleConstants::Normal;
	constexpr uint32_t FONT_STYLE_BOLD = FontStyleConstants::Bold;
	constexpr uint32_t FONT_STYLE_ITALIC = FontStyleConstants::Italic;
	constexpr uint32_t FONT_STYLE_UNDERLINE = FontStyleConstants::UnderLine;
	constexpr uint32_t FONT_STYLE_STRIKEOUT = FontStyleConstants::StrikeOut;
	constexpr uint32_t FONT_STYLE_CUSTOMWEIGHT = FontStyleConstants::CustomWeight;

	class TOAPI UIFont
	{
	public:
		//创建默认字体
		UIFont(LOGFONTW* info = nullptr);
		//创建字体自字体族
		UIFont(LPCWSTR lpwzFontFace, INT dwFontSize = 0, DWORD dwFontStyle = FONT_STYLE_NORMAL);
		~UIFont();
		//加载字体文件自内存
		static BOOL LoadFromMem(LPVOID data, size_t size, LPCWSTR lpwzFontFace);
		//字体取描述表
		LPVOID GetContext(INT index);
		//字体取尺寸
		INT GetSize();
		//获取逻辑字体
		BOOL GetLogFont(LOGFONTW* lpLogFont);
		//获取默认逻辑字体
		BOOL GetDefaultLogFontA(LOGFONTA* lpLogFont);
		BOOL GetDefaultLogFontW(LOGFONTW* lpLogFont);
	protected:
		LPVOID m_context = nullptr;
	};
} // namespace HHBUI

// 全局兼容性命名空间（在HHBUI命名空间外部）
namespace FontStyle
{
	constexpr uint32_t Normal = HHBUI::FontStyleConstants::Normal;
	constexpr uint32_t Bold = HHBUI::FontStyleConstants::Bold;
	constexpr uint32_t Italic = HHBUI::FontStyleConstants::Italic;
	constexpr uint32_t UnderLine = HHBUI::FontStyleConstants::UnderLine;
	constexpr uint32_t StrikeOut = HHBUI::FontStyleConstants::StrikeOut;
	constexpr uint32_t CustomWeight = HHBUI::FontStyleConstants::CustomWeight;
}

